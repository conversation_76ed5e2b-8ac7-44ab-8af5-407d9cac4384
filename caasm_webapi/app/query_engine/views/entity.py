from __future__ import annotations

import csv
import json
import logging
import os
import random
import re
import traceback
from collections import defaultdict
from copy import copy
from datetime import datetime, timedelta
from io import String<PERSON>
from typing import List, Union

from django.http import FileResponse, QueryDict, StreamingHttpResponse
from django.shortcuts import redirect
from django.utils.encoding import escape_uri_path
from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_aql.asql_plus import AsqlPlusLogicalQuery, AsqlPlusResolver
from caasm_aql.base import AsqlType
from caasm_aql.querier import LogicalGroupQuerier, GraphQuerier, QuerierResult
from caasm_aql.queriers.es import ESLogicalGroupQuerier
from caasm_aql.queriers.orient import OrientGraphQuerier
from caasm_aql.query_filters.consts import QueryFilterType
from caasm_aql.query_filters.runtime import query_filter_manager
from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_meta_data.constants import Category
from caasm_persistence.handler.runtime import es_handler
from caasm_render.loaders.cards.adapter_card import AdapterCardHandler
from caasm_render.loaders.cards.fabric_card import FabricCardHandler
from caasm_render.loaders.constants import CardType
from caasm_render.query.base import BaseQuery
from caasm_render.runtime import render_manager
from caasm_service.constants.export import ExportRecordStatus
from caasm_service.entity.asset_type import AssetType
from caasm_service.entity.category_view import CategoryViewEntity, QueryFilterEntity
from caasm_service.entity.entity_view import EntityViewEntity
from caasm_service.entity.meta_entity_fields import MetaEntityFields
from caasm_service.entity.meta_entity_type_view import MetaEntityTypeViewEntity
from caasm_service.entity.meta_model import MetaFieldType, MetaField, MetaModel, MetaView
from caasm_service.entity.meta_model import TYPE_NAME_MAPPER
from caasm_service.entity.user_default_entity_fields import UserEntityDefaultFields
from caasm_service.entity.user_favorite import UserFavoriteEntity
from caasm_service.runtime import (
    adapter_service,
    meta_view_service,
    meta_asset_type_view_service,
    entity_service,
    user_asset_aql_history_service,
    user_default_entity_fields_service,
    meta_entity_fields_service,
    join_export_fields_service,
    meta_entity_type_service,
    export_record_service,
    category_view_service,
    entity_view_service,
    asset_type_service,
    meta_model_service,
    business_portraiter_service,
    meta_field_service,
)
from caasm_service.runtime import user_favorite_service
from caasm_service.schema.runtime import query_filter_schema
from caasm_web_tool.mem_cache import local_cache
from caasm_tool.util import extract, deduplicate, compute_md5
from caasm_webapi.app.query_engine.requests.entity import (
    ExportRequest,
    AqlRequest,
    EntityTypeViewConfigRequest,
    SceneViewRequest,
    ENTITY_TYPE_NONE_VALUE,
    QueryFilterRequest,
)
from caasm_webapi.app.query_engine.requests.entity import (
    UserDefaultEntityFieldsRequest,
    QueryAttributeRequest,
)
from caasm_webapi.app.query_engine.serializers.entity import (
    ExportRequestSerializer,
    AqlRequestSerializer,
    EntitiesFieldsResultSerializer,
    EntityDetailRequestSerializer,
    EntityCategoryDetailRequestSerializer,
    EntitySupportCardSerializer,
    EntitySupportCardRequestSerializer,
    EntityCardDetailRequestSerializer,
    EntityAdapterMenuResponseSerializer,
    AqlFieldValueGroupRequestSerializer,
    UpdateUserDefaultEntityFieldsRequestSerializer,
    EntityFieldsSerializer,
    JoinExportFieldsSerializer,
    EntityTypeSerializer,
    ExportTaskRequestSerializer,
    SceneViewConfigResponseSerializer,
    EntityTypeViewResponseSerializer,
    EntityTypeViewRequestSerializer,
    CategoryRequestSerializer,
    AsqlResponseSerializer,
    GetQueryFilterRequestSerializer,
    GetQueryFilterResponseSerializer,
    QueryAttributeRequestSerializer,
    FavoriteAttributeRequest,
    OwnershipFieldGroupRequestSerializer,
    EntityFavorityTypeSerializer,
)
from caasm_webapi.app.query_engine.responses.entity import OwnershipFieldSerializer
from caasm_webapi.conf import settings
from caasm_webapi.util.response import build_success, build_failed, ResponseCode
from caasm_webapi.util.serializer import SnapshotDateSerializer
from caasm_webapi.util.tool import get_user_id, get_user, generate_data_filter, generate_image_data
from caasm_workflow.sdk.workflow import workflow_sdk
from caasm_service.constants.asset import MAPPER_ATTRI_TO_OWNERSHIP
from caasm_tool.constants import DATETIME_FORMAT

from caasm_webapi.app.query_engine.requests.vuln import VulnAggsWithVulnNameRequest
from caasm_webapi.app.query_engine.serializers.vuln import VulnInstanceAggeWithVulnNameRequestSerializer


log = logging.getLogger()


def get_value(obj, field_define):
    fields = field_define.split(".")
    for field in fields:
        if isinstance(obj, dict):
            if field in obj:
                obj = obj[field]
                return obj
    return ""


def _search(value, keyword: str) -> bool:
    if isinstance(value, list):
        for item in value:
            result = _search(item, keyword)
            if result:
                return True
    elif isinstance(value, dict):
        for k, v in value.items():
            result = _search(v, keyword)
            if result:
                return True
    elif value is None:
        return False
    else:
        return keyword in str(value)
    return False


class EntityDetailCommonAPI(APIView, BaseQuery):
    default_serializer = EntitySupportCardRequestSerializer
    entity_id_name = "entityId"

    def handle(self, category, request_data):
        serializer = self.default_serializer(data=request_data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.INVALID_DATA, message=serializer), None, None

        validated_data = serializer.validated_data
        entity_id = validated_data[self.entity_id_name]
        date = validated_data["date"]
        # if category == Category.BUSINESS:
        #     entity_data = business_portraiter_service.get_by_id_direct(id=entity_id)
        # else:
        entity_data = self.get_entity_data(category, date, entity_id)
        if not entity_data:
            return build_failed(ResponseCode.NOT_FOUND), None, None
        return None, entity_data, validated_data


class MetaViewBase(BaseQuery):
    category_map = {Category.VUL_INSTANCE_UNIQUE: [Category.VUL_INSTANCE_UNIQUE, Category.ASSET, Category.VUL]}

    @classmethod
    def _remove_field(cls, field_name, meta_fields):
        found = False
        field_split = field_name.split(".")
        base_name = field_split[0]
        for field in meta_fields:
            if found:
                break
            if field["name"] == base_name and len(field_split) > 1:
                index = 1
                last_field = field
                while True:
                    if index == len(field_split):
                        break
                    index += 1
                    children_fields = last_field.get("children", [])
                    for child_field in children_fields:
                        if child_field["name"] == field_name:
                            last_field = child_field
                            if index == len(field_split):
                                children_fields.remove(child_field)
                                found = True
                            break

    def clean_field(self, meta_models, meta_field_mapper):
        result = []
        checker = set()
        for meta_model in meta_models:
            meta_model: MetaModel = meta_model
            tmp_meta_fields = meta_field_mapper.get(meta_model.id)
            if not tmp_meta_fields:
                continue
            tmp_fields = self._clean_field(tmp_meta_fields, checker=checker)
            details = [
                {
                    "label": "字段名称",
                    "value": meta_model.name,
                },
                {
                    "label": "显示名称",
                    "value": meta_model.display_name,
                },
                {
                    "label": "字段类型",
                    "value": TYPE_NAME_MAPPER.get(meta_model.type.value, ""),
                },
                {"label": "描述信息", "value": meta_model.description},
            ]
            tmp_result = {
                "name": meta_model.name,
                "display_name": meta_model.display_name,
                "full_name": meta_model.name,
                "full_display_name": meta_model.display_name,
                "details": details,
                "children": tmp_fields,
                "type": MetaFieldType.OBJECT,
            }
            result.append(tmp_result)
        return result

    def _clean_field(self, fields, checker, result=None):
        if result is None:
            result = []
        for field in fields:
            if field.full_name in checker:
                continue
            checker.add(field.full_name)
            if field.hidden:
                continue

            data_type = field.type
            children = field.children
            name = field.full_name
            display_name = field.display_name
            full_display_name = field.full_display_name
            description = field.description
            is_complex = field.is_complex

            sub_type = ""
            if data_type == MetaFieldType.LIST:
                sub_type = TYPE_NAME_MAPPER.get(children[0].type)
                if children and children[0].type == MetaFieldType.OBJECT:
                    children = children[0].children
                else:
                    children = []
            details = [
                {
                    "label": "字段名称",
                    "value": name,
                },
                {
                    "label": "显示名称",
                    "value": display_name,
                },
                {
                    "label": "字段类型",
                    "value": TYPE_NAME_MAPPER.get(data_type, ""),
                },
                {"label": "描述信息", "value": description},
            ]
            if sub_type:
                details.insert(3, {"label": "元素类型", "value": sub_type})
            tmp_result = {
                "name": name,
                "full_name": field.full_name,
                "data_type": data_type,
                "display_name": display_name,
                "full_display_name": full_display_name,
                "details": details,
                "children": self._clean_field(children, checker=checker),
                "isComplexField": is_complex,
                "is_selectable": self._is_selectable(field),
            }
            result.append(tmp_result)
        return result

    def _is_selectable(self, field: MetaField):
        return True


class QueryFilterRenderBase:
    @classmethod
    def _calculate_query_filters(
        cls, query_filters, global_asql, category, date=None, entity_type=None, field_mapper=None, init=True
    ):
        query_filters = query_filter_manager.calculate_filters(
            query_filters, global_asql, category, date, entity_type, field_mapper, init
        )
        if query_filters is None:
            return [], None
        venn_filter = None
        for query_filter in query_filters:
            if query_filter.type == QueryFilterType.VENN.value:
                venn_filter = query_filter
                break
        if venn_filter is not None:
            query_filters.remove(venn_filter)
        return query_filters, venn_filter

    @classmethod
    def _calculate_query_filter(
        cls,
        query_filter_id: Union[str, List[str]],
        query_filters,
        global_asql,
        category,
        date=None,
        entity_type=None,
        field_mapper=None,
        init=True,
    ):
        if isinstance(query_filter_id, str):
            selected_query_filter = None
            for query_filter in query_filters:
                if query_filter.filter_id == query_filter_id:
                    selected_query_filter = query_filter
                    break
            if selected_query_filter:
                query_filters.remove(selected_query_filter)
                return query_filter_manager.calculate_filter(
                    selected_query_filter,
                    query_filters,
                    global_asql,
                    category,
                    date,
                    entity_type,
                    field_mapper,
                    init=init,
                )
        else:
            return query_filter_manager.calculate_filters(
                query_filters, global_asql, category, date, entity_type, field_mapper, init, query_filter_id
            )


class AqlQueryAPI(APIView, BaseQuery, QueryFilterRenderBase):
    def __init__(self, *args, **kwargs):
        super(AqlQueryAPI, self).__init__(*args, **kwargs)
        self._high_light_value_compare_method_mapper = {
            "regex": self.__high_light_value_regex_compare_method,
        }
        self._high_light_value_convert_method_mapper = {
            "enum": self.__high_light_value_enum_convert_method,
            "version": self.__high_light_value_version_convert_method,
        }
        self._fields = []
        self._field_keyword_map = {}
        self._default_query_fields = ["base.query_content"]
        self._basic_result = None
        self._aql_request: AqlRequest = None
        self._high_lights = []
        self._data_filters: dict = {}

    def post_pre(self, request, category):
        serializer = AqlRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        category_lst = category.split(".")
        if len(category_lst) > 1:
            category = category_lst[-1]

        self._aql_request: AqlRequest = serializer.save()
        # 添加总账过滤时间
        if category == Category.TOTAL_ASSET:
            field_mapper = self.find_field_to_mapper(category)
            resolver = AsqlPlusResolver(as_fulltext=True)
            # build start_date and end_date filter
            query = None
            if self._aql_request.option.start_date:
                try:
                    asql = f"asset_base.create_on >= '{datetime.fromtimestamp(self._aql_request.option.start_date).strftime(DATETIME_FORMAT)}'"
                    query = resolver.resolve_logic_query(asql)
                except Exception:
                    pass
            if self._aql_request.option.end_date:
                try:
                    asql = f"asset_base.update_on <= '{datetime.fromtimestamp(self._aql_request.option.end_date).strftime(DATETIME_FORMAT)}'"
                    query = resolver.resolve_logic_query(asql)
                except Exception:
                    pass
            if query is not None:
                self._aql_request.filters.extend([q.to_dict(field_mapper) for q in query.queries])
        if self._aql_request.full_fields:
            self._meta_field_mapper = self.find_field_to_mapper(category, date=self._aql_request.date)
        else:
            (
                self._meta_field_mapper,
                self._common_field_mapper,
                self._entity_field_mapper,
                _,
                _,
                _,
            ) = self.find_fields_of_entity_type(
                category, date=self._aql_request.date, entity_type=self._aql_request.entity_type
            )
        self._data_filters = generate_data_filter(request, category)
        if self._data_filters:
            self._aql_request.filters.append(self._data_filters)
        # self._meta_field_mapper = self.find_field_to_mapper(category, date=self._aql_request.date)
        if self._aql_request.option.field_list:
            all_child_retrieve_info = self._aql_request.childs_retrieve_info
            if all_child_retrieve_info:
                for child_retrieve in all_child_retrieve_info:
                    if child_retrieve.field in self._aql_request.option.field_list:
                        self._fields.append(child_retrieve.field)
                        self._field_keyword_map[child_retrieve.field] = child_retrieve.keyword
        entity_ids = None
        if self._aql_request.related_entity_option:
            entity_dict = entity_service.get_entity(
                self._aql_request.related_entity_option.parent_category,
                "base.entity_id",
                self._aql_request.related_entity_option.entity_id,
                self._aql_request.date,
                fields=[self._aql_request.related_entity_option.field],
            )
            if entity_dict:
                entity_ids = extract(entity_dict, self._aql_request.related_entity_option.field)
                if not isinstance(entity_ids, List):
                    entity_ids = []
                if entity_ids:
                    if not all([isinstance(item, str) for item in entity_ids]):
                        entity_ids = []
        query_filters = []
        if self._aql_request.query_filters:
            query_filters.extend(self._aql_request.query_filters)
        if self._aql_request.venn_filter:
            query_filters.append(self._aql_request.venn_filter)

        aql = self._get_asql(
            self._aql_request.aql,
            category,
            self._aql_request.date,
            self._aql_request.filters,
            self._aql_request.entity_type,
            query_filters,
        )
        if aql is None:
            aql = ""
        querier: LogicalGroupQuerier = ESLogicalGroupQuerier(self._meta_field_mapper)

        if self._aql_request.aql_type == AsqlType.ASGL and aql:
            querier: GraphQuerier = OrientGraphQuerier(querier)
        else:
            high_aql = self._get_asql(
                self._aql_request.aql,
                category,
                self._aql_request.date,
                self._aql_request.filters,
                None,
                query_filters,
            )
            _, self._high_lights, _ = querier.parse_aql(
                category,
                high_aql,
                self._aql_request.option,
                self._aql_request.additions,
                entity_ids=entity_ids,
            )
            query_fields = []
            for high_light in self._high_lights:
                field = high_light["name"]
                if field in query_fields:
                    continue
                query_fields.append(field)

            if self._high_lights:
                self._aql_request.option.field_list += query_fields
            else:
                if self._aql_request.aql or self._aql_request.filters:
                    self._aql_request.option.field_list += self._default_query_fields

        try:
            _category = ".".join(category_lst)
            self._basic_result = querier.query_for_view(
                aql,
                _category,
                self._aql_request.option,
                d=self._aql_request.date,
                additions=self._aql_request.additions,
                entity_ids=entity_ids,
            )
        except Exception as exc:
            logging.debug(traceback.format_exc())
            return build_failed(ResponseCode.SYSTEM_ERROR, message=str(exc))
        if self._basic_result.errors:
            if isinstance(self._basic_result.errors, list):
                error_msg = "，".join(self._basic_result.errors)
            else:
                error_msg = self._basic_result.errors
            return build_failed(ResponseCode.REQUEST_ERROR, message=error_msg)

    def after_post(self, request, category):
        pk_field = self.get_pk_field(category)

        for item in self._basic_result.data:
            for field in self._fields:
                values = self.extract(item, field)
                keyword = self._field_keyword_map.get(field)
                if not keyword or not values:
                    continue
                keyword_info = list()
                for row in values:
                    if _search(row, keyword):
                        keyword_info.append(row)

                _fields = field.split(".")
                if len(_fields) == 1:
                    item[_fields] = keyword_info
                first_field = _fields[0]
                next_field = ".".join(_fields[1:])
                item[first_field][next_field] = keyword_info

        field_list = self._aql_request.option.field_list or []
        user = get_user(request)
        if category == Category.ASSET and user and getattr(user, "user_id", None):
            self._record_asset_aql_history(self._aql_request, category, user.user_id)

        records = []
        adapter_by_name = {}
        for row in self._basic_result.data:
            entity_id = extract(row, pk_field)
            new_row = self._render_row(row, field_list, self._meta_field_mapper)
            new_row["key"] = entity_id

            adapter_names = extract(row, "base.adapters") or []
            if adapter_names:
                adapter_mapper = {}
                not_cached_adapter_names = []
                for adapter_name in adapter_names:
                    if adapter_name in adapter_by_name:
                        adapter_mapper[adapter_name] = adapter_by_name[adapter_name]
                    else:
                        not_cached_adapter_names.append(adapter_name)
                if not_cached_adapter_names:
                    adapters = adapter_service.find_adapter(
                        names=not_cached_adapter_names, fields=["name", "display_name"]
                    )
                    not_cached_adapters = {i.name: i.display_name for i in adapters}
                    adapter_mapper.update(not_cached_adapters)
                    adapter_by_name.update(not_cached_adapters)
            else:
                adapter_mapper = {}

            adapter_show_info = []
            for adapter_name in adapter_names:
                adapter_info = {"name": adapter_name, "display_name": adapter_mapper.get(adapter_name, "无")}
                adapter_show_info.append(adapter_info)

            if not self._high_lights:
                high_light_result = self._render_high_light_by_keyword(row)
            else:
                high_light_result = self._render_high_light_by_aql(row)

            new_row.pop("base.query_content", None)

            new_row["adapters"] = adapter_show_info
            new_row["high_light"] = high_light_result
            records.append(new_row)

        # #   筛选器
        # query_filters, venn_filter = self._calculate_query_filters(
        #     self._aql_request.query_filters,
        #     self._aql_request.aql,
        #     category,
        #     self._aql_request.date,
        #     self._aql_request.entity_type,
        #     self._meta_field_mapper,
        # )

        return build_success(
            AsqlResponseSerializer(
                instance={
                    "count": self._basic_result.count,
                    "raw": records,
                    # "query_filters": query_filters,
                    # "venn_filter": venn_filter,
                }
            ).data
        )

    def post(self, request, category):
        if category == Category.VULN_ASSET_VIEW:
            category = Category.VUL_INSTANCE_UNIQUE
            aggs_source = []
            aggs_source.extend([{"cve_id": {"terms": {"field": "vul_instance_unique.asset.display_value"}}}])
            result = self.post_vuln_view(request, category, aggs_source)
            return build_success(
                AsqlResponseSerializer(
                    instance={
                        "count": self._basic_result.count,
                        "raw": result,
                    }
                ).data
            )
        if category == Category.VULN_AGGES_VIEW:
            category = Category.VUL_INSTANCE_UNIQUE
            aggs_source = []
            aggs_source.extend(
                [
                    {"cve_id": {"terms": {"field": "vul_instance_unique.cve_id"}}},
                    {"name": {"terms": {"field": "vul_instance_unique.name"}}},
                ]
            )
            records = self.post_vuln_view(request, category, aggs_source)
            result = self.after_aggs_post(records)
            return build_success(
                AsqlResponseSerializer(
                    instance={
                        "count": self._basic_result.count,
                        "raw": records,
                    }
                ).data
            )
        response = self.post_pre(request, category)
        if response:
            return response
        result = self.after_post(request, category)
        return result

    def after_aggs_post(self, records: List[dict]):
        for row in records:
            rel_ids = row.get("rel_ids", {}).get("rendered_value", [])
            if rel_ids:
                row["rel_ids"] = {
                    "rendered_value": len(rel_ids),
                    "value": {
                        "value": rel_ids,
                        "asql": f"base.entity_id.in({rel_ids})",
                    },
                }

        return records

    def post_vuln_view(self, request, category, aggs_source) -> list:
        serializer = VulnInstanceAggeWithVulnNameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        self._aql_request: VulnAggsWithVulnNameRequest = serializer.save()
        self._data_filters = generate_data_filter(request, category)
        if self._data_filters:
            self._aql_request.filters.append(self._data_filters)
        self._meta_field_mapper = self.find_field_to_mapper(category, date=self._aql_request.date)
        entity_ids = None
        query_filters = []
        if self._aql_request.query_filters:
            query_filters.extend(self._aql_request.query_filters)
        if self._aql_request.venn_filter:
            query_filters.append(self._aql_request.venn_filter)

        aql = self._get_asql(
            self._aql_request.aql,
            category,
            self._aql_request.date,
            self._aql_request.filters,
            self._aql_request.entity_type,
            query_filters,
        )
        if aql is None:
            aql = ""
        global_asql = self._get_asql(
            self._aql_request.aql,
            category,
            self._aql_request.date,
            self._aql_request.filters,
            self._aql_request.entity_type,
        )
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))
        aggs = {
            "composite_buckets": {
                "composite": {
                    "size": 10000,
                    "sources": aggs_source,
                },
            }
        }
        global_condition, _, _ = querier.parse_aql(Category.VUL_INSTANCE_UNIQUE, global_asql)
        count = entity_service.composite_count(
            Category.VUL_INSTANCE_UNIQUE,
            None,
            condition=global_condition,
            aggs=aggs,
            fields=self._aql_request.fields,
        )
        pre_size = (self._aql_request.option.page_index - 1) * self._aql_request.option.page_size
        if pre_size >= count:
            return build_success(data={"count": count, "data": []})
        aggs["composite_buckets"]["composite"]["size"] = self._aql_request.option.page_size
        data = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=global_condition,
            aggs=aggs,
            fields=self._aql_request.option.field_list,
        )
        if pre_size > 0:
            after_key = data.get("aggregations", {}).get("composite_buckets", {}).get("after_key", None)
            if after_key:
                aggs["composite_buckets"]["composite"]["after"] = after_key
        else:
            after_key = None
        aggs["composite_buckets"]["aggs"] = {
            "rel_ids": {
                "terms": {"field": "vul_instance_unique.asset.rel_id", "size": 10000},
            }
        }
        for field in self._aql_request.option.field_list:
            aggs["composite_buckets"]["aggs"][field] = {"terms": {"field": field, "size": 10000}}
        res = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=global_condition,
            aggs=aggs,
            fields=self._aql_request.option.field_list,
        )
        result = self.paser_aggs(res)
        self._basic_result = QuerierResult()
        self._basic_result.count = len(result)
        self._basic_result.data = result
        self._fields = list(set(self._fields))

        # render处理
        records = []
        adapter_by_name = {}
        for row in self._basic_result.data:
            try:
                entity_id = extract(row, self._default_pk_field)
                new_row = {}
                for field_name in self._fields:
                    value = row.get(field_name, None)
                    meta_field = self._meta_field_mapper.get(field_name)
                    if (
                        isinstance(value, list)
                        and len(value) == 1
                        and meta_field
                        and meta_field.type != MetaFieldType.LIST
                    ):
                        value = value[0]
                    if meta_field:
                        render_value = render_manager.render(value, meta_field)
                    else:
                        # return {"value": value, "rendered_value": value}
                        render_value = {"value": value, "rendered_value": value}
                    new_row[field_name] = render_value

                new_row["key"] = row.get("key")

                adapter_names = row.get("base.adapters", [])
                if adapter_names:
                    adapter_mapper = {}
                    not_cached_adapter_names = []
                    for adapter_name in adapter_names:
                        if adapter_name in adapter_by_name:
                            adapter_mapper[adapter_name] = adapter_by_name[adapter_name]
                        else:
                            not_cached_adapter_names.append(adapter_name)
                    if not_cached_adapter_names:
                        adapters = adapter_service.find_adapter(
                            names=not_cached_adapter_names, fields=["name", "display_name"]
                        )
                        not_cached_adapters = {i.name: i.display_name for i in adapters}
                        adapter_mapper.update(not_cached_adapters)
                        adapter_by_name.update(not_cached_adapters)
                else:
                    adapter_mapper = {}

                adapter_show_info = []
                for adapter_name in adapter_names:
                    adapter_info = {"name": adapter_name, "display_name": adapter_mapper.get(adapter_name, "无")}
                    adapter_show_info.append(adapter_info)
                new_row["adapters"] = adapter_show_info
                records.append(new_row)
            except Exception as e:
                pass

        return records

    def paser_aggs(self, data):
        result = []
        for bucket in data.get("aggregations", {}).get("composite_buckets", {}).get("buckets", []):
            bucket_item = {}
            for k, v in bucket.items():
                if k == "key":
                    json_str = json.dumps(v, sort_keys=True, ensure_ascii=False, separators=(",", ":"))
                    bucket_item[k] = compute_md5(json_str.encode("utf-8"))
                    continue
                self._fields.append(k)
                if k == "rel_ids":
                    bucket_item["rel_ids"] = [item.get("key") for item in v.get("buckets", [])]
                    continue
                if isinstance(v, dict):
                    item_value = [item.get("key") for item in v.get("buckets", [])]
                    bucket_item[k] = item_value
            result.append(bucket_item)
        return result

    @classmethod
    def _render_row(cls, row, field_list, meta_field_mapper):
        result = {}
        for field_name in field_list:
            value = extract(row, field_name)
            meta_field = meta_field_mapper.get(field_name)
            if meta_field:
                render_value = render_manager.render(value, meta_field)
            else:
                # return {"value": value, "rendered_value": value}
                render_value = {"value": value, "rendered_value": value}
            result[field_name] = render_value
        return result

    def _render_high_light_by_keyword(self, row):
        result = []
        query_content = extract(row, "base.query_content")
        if not query_content:
            return result

        query_contents = query_content.split("!@##@!")
        for _query_content in query_contents:
            if "=" in _query_content:
                field_name, field_value = _query_content.split("=", 1)
            else:
                field_name, field_value = None, None
            if field_name:
                meta_field = self._meta_field_mapper.get(field_name)
                if meta_field and self.is_exist_filter_value(field_value):
                    result.append(
                        {"display_name": meta_field.full_display_name, "name": field_name, "value": field_value}
                    )
        return result

    def is_exist_filter_value(self, field_value):
        for filter_query in self._aql_request.filters:
            if filter_query.get("asql", None):
                sql_filter_value = filter_query["asql"]
                sql_filter_value = sql_filter_value.strip("'")
                if sql_filter_value in field_value:
                    return True
        return False

    def _render_high_light_by_aql(self, row):
        result_mapper = {}

        for high_light in self._high_lights:
            _high_light_field = high_light["name"]

            _meta_field = self._meta_field_mapper[_high_light_field]

            _high_light_value = high_light["value"]

            _value = self.__parse_high_light_value(row, _meta_field, high_light)

            if _value:
                if _high_light_field not in result_mapper:
                    _record = {
                        "display_name": _meta_field.full_display_name,
                        "name": _high_light_field,
                        "value": _value,
                    }
                    result_mapper[_high_light_field] = _record
                else:
                    _tmp_values = result_mapper[_high_light_field]["value"]
                    _tmp_values.extend(_value)
                    result_mapper[_high_light_field]["value"] = deduplicate(_tmp_values)

        for key, val in result_mapper.items():
            value = val["value"]
            val["value"] = ",".join(map(str, "" if value is None else value))
        return list(result_mapper.values())

    def __parse_high_light_value(self, row, meta_field, high_light):
        result = []
        _high_light_flag = high_light["flag"]
        _high_light_value = high_light["value"]
        _meta_field_name = meta_field.full_name
        if "vul_instance_unique" in row:
            row = row["vul_instance_unique"]

        _row_values = extract(row, _meta_field_name)
        if _row_values is None:
            log.warning(f"Not found {_meta_field_name} any value. please check, record is is {row.get('_id')} ")
            return None

        _row_values = _row_values if isinstance(_row_values, list) else [_row_values]
        _high_light_values = _high_light_value if isinstance(_high_light_value, list) else [_high_light_value]

        compare_func = self.__get_high_light_compare_method(_high_light_flag)
        convert_func = self.__get_high_light_convert_method(meta_field.type)
        for _row_value in _row_values:
            for _high_light_value in _high_light_values:
                _value = convert_func(_row_value, _high_light_value)
                if compare_func(_value, _high_light_value):
                    result.append(_value)

        return deduplicate(result)

    def __get_high_light_compare_method(self, high_light_flag):
        _compare_method = self._high_light_value_compare_method_mapper.get(high_light_flag)
        if not _compare_method:
            return self.__high_light_value_default_compare_method
        return _compare_method

    def __get_high_light_convert_method(self, meta_field_type):
        _convert_method = self._high_light_value_convert_method_mapper.get(meta_field_type)
        if not _convert_method:
            return self.__high_light_value_default_convert_method
        return _convert_method

    @classmethod
    def __high_light_value_enum_convert_method(cls, row_value, _high_light_value):
        return row_value.get("text") if isinstance(_high_light_value, str) else row_value.get("value")

    @classmethod
    def __high_light_value_version_convert_method(cls, row_value, _high_light_value):
        return row_value.get("plain")

    @classmethod
    def __high_light_value_default_convert_method(cls, row_value, _high_light_value):
        return row_value

    @classmethod
    def __high_light_value_regex_compare_method(cls, raw_value, high_light_value):
        return re.match(".*" + high_light_value + ".*", raw_value)

    @classmethod
    def __high_light_value_default_compare_method(cls, raw_value, high_light_value):
        return raw_value == high_light_value

    def _record_asset_aql_history(self, aql_request, category, user_id):
        query_filters = []
        if aql_request.query_filters:
            query_filters.extend(aql_request.query_filters)
        global_asql = self._get_asql(
            aql_request.aql, category, aql_request.date, aql_request.filters, aql_request.entity_type
        )
        if aql_request.aql:
            aql = aql_request.aql
        elif aql_request.filters:
            aql = AsqlPlusLogicalQuery.from_list(aql_request.filters)
        else:
            aql = ""
        if not (global_asql and user_id):
            return

        # digest = compute_md5(aql)

        # user_asset_aql_history = user_asset_aql_history_service.get_user_aql_history(user_id=user_id, digest=digest)
        # if user_asset_aql_history:
        # 更新更新时间
        # user_asset_aql_history_service.update(user_asset_aql_history)
        # return
        aql_type = aql_request.aql_type.value
        if aql_request.query_filters:
            query_filters = query_filter_schema.dump(aql_request.query_filters, many=True)
        else:
            query_filters = None
        if aql_request.venn_filter:
            venn_filter = query_filter_schema.dump(aql_request.venn_filter)
        else:
            venn_filter = None
        #   单个用户最多不超过100个历史
        # user_asset_aql_history_service.delete_ids_of_exceeded_history(category, user_id, 99)
        user_asset_aql_history_service.save_user_asset_aql_history(
            user_id=user_id,
            category=category,
            aql=aql,
            aql_type=aql_type,
            entity_type=aql_request.entity_type,
            query_filters=query_filters,
            venn_filter=venn_filter,
        )


class AqlQuerySimpleAPI(AqlQueryAPI):
    def __init__(self, *args, **kwargs):
        super(AqlQuerySimpleAPI, self).__init__(*args, **kwargs)
        self._default_query_fields = []

    def after_post(self, request, category):
        result = {"count": self._basic_result.count, "raw": self._basic_result.data}
        return build_success(result)


class EntityTypeViewBase(MetaViewBase, QueryFilterRenderBase):
    def _get_entity_type_view(self, category, date, common_model_names=None, entity_type=None, default_fields=None):
        if category == Category.TOTAL_ASSET:
            category = Category.ASSET
        key = f"entity_type_view:{category}:{common_model_names}:{entity_type}:{default_fields}"
        value = local_cache.get(key)
        if value is None:
            if not default_fields:
                entity_view: EntityViewEntity = entity_view_service.get_view(category, entity_type)
                if entity_view is None:
                    entity_view = entity_view_service.get_view(category, None)
                    if not entity_view:
                        entity_view = entity_view_service.get_view(None, None)
                default_fields = entity_view.default_fields
            if common_model_names is None:
                category_view: CategoryViewEntity = category_view_service.get_view(category)
                if category_view is None:
                    category_view: CategoryViewEntity = category_view_service.get_view(None)
                common_model_names = category_view.common_models
            common_fields = self._get_common_fields(category, common_model_names)
            common_fields_names = set()
            for common_field in common_fields:
                common_fields_names.add(common_field["name"])
            if entity_type is None:
                meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, date)
            else:
                meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, date, entity_type)
            fields = self.clean_field(meta_models, meta_field_mapper)
            final_fields = []
            for field in fields:
                if field["name"] in common_fields_names:
                    continue
                final_fields.append(field)
            fields = final_fields
            local_cache.set(key, (common_fields, default_fields, fields), random.randint(300, 600))
        else:
            common_fields, default_fields, fields = value
        return common_fields, default_fields, fields

    def _get_common_fields(self, category, model_names):
        common_models, common_fields = self.find_model_and_field(category, model_names=model_names)
        field_mapper = defaultdict(list)
        for field in common_fields:
            field_mapper[field.model_id].append(field)
        result = {meta_model.id: field_mapper[meta_model.id] for meta_model in common_models}
        return self.clean_field(common_models, result)

    def _get_entity_types(self, category, date=None) -> List[dict]:
        table = entity_service.get_table(category, date)
        if table is None:
            return []
        result = entity_service.search(
            table,
            aggs={"entity_types": {"terms": {"field": "base.asset_type", "size": 10000, "order": {"_count": "desc"}}}},
        )
        entity_types = []
        entity_type_names = []
        total = 0
        asset_type_mapper = {}
        for asset_type in asset_type_service.find_asset_type():
            asset_type: AssetType = asset_type
            asset_type_mapper[asset_type.name] = asset_type.display_name
        for entity_type in extract(result.body, "aggregations.entity_types.buckets") or []:
            count = entity_type["doc_count"]
            entity_types.append(
                {
                    "value": entity_type["key"],
                    "label": asset_type_mapper.get(entity_type["key"], entity_type["key"]),
                    "count": count,
                }
            )
            total += count
            entity_type_names.append(entity_type["key"])
        return entity_types

    def _get_view(
        self,
        category,
        date,
        entity_type=None,
        query_filters=None,
        default_fields=None,
        asql=None,
        asql_type=AsqlType.ASQL_PLUS.value,
        init=True,
    ):
        merged_query_filters = {}
        category_view = category_view_service.get_view(category)
        if category_view is None:
            category_view = category_view_service.get_view(None)
        if category_view.query_filters:
            for query_filter in category_view.query_filters:
                if query_filter.type == QueryFilterType.VENN.value:
                    filter_id = query_filter_manager.get_id(query_filter)
                    query_filter.filter_id = filter_id
                    merged_query_filters[filter_id] = query_filter
        if query_filters:
            for query_filter in query_filters:
                if query_filter.type == QueryFilterType.VENN.value:
                    merged_query_filters[query_filter.filter_id] = query_filter
        query_filters, venn_filter = self._calculate_query_filters(
            list(merged_query_filters.values()), "", category, date, entity_type, init=init
        )
        result = {
            "category": category_view.category,
            "common_models": category_view.common_models,
            "venn_filter": venn_filter,
        }
        common_fields, default_fields, fields = self._get_entity_type_view(
            category, date, category_view.common_models, entity_type, default_fields
        )
        result["common_fields"] = common_fields
        result["default_fields"] = default_fields
        result["fields"] = fields
        meta_view = meta_view_service.get_view(category)
        result["pk_field"] = meta_view.pk_field
        if entity_type is None:
            result["entity_type"] = ENTITY_TYPE_NONE_VALUE
        else:
            result["entity_type"] = entity_type
        result["asql"] = asql or ""
        result["asql_type"] = asql_type
        return result

    def get_default_attributes(self, category) -> List[QueryFilterEntity]:
        category_view = category_view_service.get_view(category)
        if not category_view:
            category_view = category_view_service.get_view(None)
        default_attribute = []
        if category_view.query_filters:
            for query_filter in category_view.query_filters:
                if query_filter.type != QueryFilterType.VENN.value:
                    filter_id = query_filter_manager.get_id(query_filter)
                    query_filter.filter_id = filter_id
                    default_attribute.append(query_filter)
        return default_attribute


class MetaViewAPI(APIView, EntityTypeViewBase):
    _ENTITY_TYPE_FIELD_NAME = "base.asset_type_display_name"
    _prefix_field_asset_base = "asset_base."

    def _get_fields(self, category, date, entity_type=None):
        meta_view: MetaView = meta_view_service.get_view(category)
        if category in [Category.VULN_AGGES_VIEW, Category.VULN_ASSET_VIEW]:
            category = Category.VUL_INSTANCE_UNIQUE
        meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, date=date)

        cleaned_fields = self.clean_field(meta_models, meta_field_mapper)
        # self._remove_field(self._ENTITY_TYPE_FIELD_NAME, cleaned_fields)
        return {
            "fields": cleaned_fields,
            "default_fields": meta_view.default_fields,
            "pk_field": meta_view.pk_field,
            "charts": meta_view.charts,
        }

    def get(self, request, category):
        serializer = SnapshotDateSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        date = serializer.save()["date"]
        serializer_instance = self._get_fields(category, date)
        user_default_fields: UserEntityDefaultFields = user_default_entity_fields_service.get_default_fields(
            get_user_id(request),
            category,
            None,
        )
        if user_default_fields:
            serializer_instance["default_fields"] = user_default_fields.field_names
        result = EntitiesFieldsResultSerializer(instance=serializer_instance).data
        return build_success(result)

    def post(self, request, category):
        # 兼容富化权属的模型字段
        serializer = EntityTypeViewRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: EntityTypeViewConfigRequest = serializer.save()
            if category == "ownership":
                fields = self.build_ownership_meta_fields(ownership_type=req.entity_type)
                field_result = OwnershipFieldSerializer(instance=fields, many=True).data
                return build_success({"fields": field_result})
            if category in [Category.VULN_AGGES_VIEW, Category.VULN_ASSET_VIEW]:
                category = Category.VUL_INSTANCE_UNIQUE
            common_fields, default_fields, fields = self._get_entity_type_view(
                category, req.date, entity_type=req.entity_type
            )
            self._remove_field(self._ENTITY_TYPE_FIELD_NAME, common_fields)
            meta_view: MetaView = meta_view_service.get_view(category)
            return build_success(
                {
                    "fields": fields,
                    "common_fields": common_fields,
                    "pk_field": meta_view.pk_field if meta_view else "base.entity_id",
                    "charts": [],
                }
            )
        return build_failed(-1, str(serializer.errors))

    @classmethod
    def build_ownership_meta_fields(cls, ownership_type: str) -> List[MetaField]:
        ownership_type = MAPPER_ATTRI_TO_OWNERSHIP[ownership_type]
        meta_model_entity = meta_model_service.get_meta_model(name="asset_base")
        fields = meta_field_service.find_meta_field(model_id=meta_model_entity.id, names=[ownership_type])
        fields = list(fields)
        fields: MetaField = fields[0]
        if fields.type == MetaFieldType.LIST:
            fields = fields.children[0].children
        return cls.filter_prefix(fields, ownership_type)

    @classmethod
    def filter_prefix(cls, meta_fields: List[MetaField], enrich_type: str):
        for meta_field in meta_fields:
            if meta_field.full_name.startswith(f"{cls._prefix_field_asset_base}{enrich_type}."):
                meta_field.full_name = meta_field.full_name.removeprefix(
                    f"{cls._prefix_field_asset_base}{enrich_type}."
                )
            else:
                meta_field.full_name = meta_field.name
            if meta_field.type == MetaFieldType.LIST:
                meta_field.children = meta_field.children[0].children
            if meta_field.children:
                meta_field.children = cls.filter_prefix(meta_field.children, enrich_type)
        return meta_fields


class ExportMetaViewAPI(APIView, MetaViewBase):
    def get(self, request: Request, category):
        """
        此处用于展示 客户 需要联合 导出的功能，需要注意的是
        1、字段集不支持多重嵌套的复杂字段
        2、优先展示客户上次导出的字段
        """

        meta_view_category_lists = self.category_map[category]
        user_export_fields = list(
            join_export_fields_service.find_vul_export_fields(
                user_id=get_user_id(request), main_category=category, sort_fields=[("create_time", -1)]
            )
        )

        category_result = {}

        user_category_exports = self.get_user_category_fields(category, user_export_fields)

        for category_info in meta_view_category_lists:
            meta_view = meta_view_service.get_view(category_info)
            meta_models, meta_field_mapper = self.find_field_to_model_mapper(category_info, date="")

            category_result[category_info] = {
                "fields": self.clean_field(meta_models, meta_field_mapper),
                "default_fields": (
                    []
                    if category_info not in user_category_exports
                    or (
                        category_info in user_category_exports
                        and not user_category_exports.get(category_info)["field_names"]
                    )
                    else user_category_exports.get(category_info)["field_names"]
                ),
                "enable": (
                    user_category_exports.get(category_info)["enable"]
                    if category_info in user_category_exports
                    else False
                ),
            }

        return build_success(data=category_result)

    def get_user_category_fields(self, category, user_export_fields):

        result = {}
        if not category or not user_export_fields:
            return result
        user_export_fields = user_export_fields[0]
        for category_info in self.category_map[category]:
            result[category_info] = {}
            if category_info == Category.ASSET:
                result[category_info]["field_names"] = user_export_fields.asset_field_names.get("field_names", [])
                result[category_info]["enable"] = user_export_fields.asset_field_names.get("enable", False)

            if category_info == Category.VUL:
                result[category_info]["field_names"] = user_export_fields.vul_field_names.get("field_names", [])
                result[category_info]["enable"] = user_export_fields.vul_field_names.get("enable", False)

            if category_info == Category.VUL_INSTANCE_UNIQUE:
                result[category_info]["field_names"] = user_export_fields.vul_unique_field_names.get("field_names", [])
                result[category_info]["enable"] = user_export_fields.vul_unique_field_names.get("enable", False)
        return result

    def _clean_field(self, fields, checker, result=None):
        if result is None:
            result = []
        for field in fields:
            if field.full_name in checker:
                continue
            checker.add(field.full_name)
            if field.hidden:
                continue

            data_type = field.type
            children = field.children
            name = field.full_name
            display_name = field.display_name
            full_display_name = field.full_display_name
            description = field.description
            is_complex = field.is_complex

            sub_type = ""
            if data_type == MetaFieldType.LIST:
                continue
            details = [
                {
                    "label": "字段名称",
                    "value": name,
                },
                {
                    "label": "显示名称",
                    "value": display_name,
                },
                {
                    "label": "字段类型",
                    "value": TYPE_NAME_MAPPER.get(data_type, ""),
                },
                {"label": "描述信息", "value": description},
            ]
            if sub_type:
                details.insert(3, {"label": "元素类型", "value": sub_type})
            tmp_result = {
                "name": name,
                "data_type": data_type,
                "display_name": display_name,
                "full_display_name": full_display_name,
                "details": details,
                "children": self._clean_field(children, checker=checker),
                "isComplexField": is_complex,
            }
            result.append(tmp_result)
        return result


class DetailAPI(APIView, BaseQuery):
    def post(self, request, category):
        request_serializer = EntityDetailRequestSerializer(data=request.data)
        if not request_serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=request_serializer)
        entity_request = request_serializer.save()

        meta_view = meta_view_service.get_view(category=category)
        if not meta_view:
            return build_failed(ResponseCode.REQUEST_ERROR, message="获取视图配置失败，请检查请求参数")

        entity_data = entity_service.get_entity(category, meta_view.pk_field, entity_request.entity_id)

        if not entity_data:
            return build_failed(ResponseCode.NOT_FOUND)

        asset_type = self._get_asset_type(entity_data)
        if not asset_type:
            return build_failed(ResponseCode.INVALID_DATA)

        meta_asset_type_view = meta_asset_type_view_service.get_meta_asset_type_view(category, asset_type)
        result = render_manager.loader(entity_data, meta_asset_type_view)
        return build_success(result)

    @classmethod
    def _get_asset_type(cls, entity):
        return extract(entity, "base.asset_type")


class CategoryImageAPI(APIView, BaseQuery):
    def get(self, request, category, name):
        default_category_dir: str = os.path.join(settings.BASE_DIR, "statics", "categories", "default")
        category_dir: str = os.path.join(settings.BASE_DIR, "statics", "categories", category)
        category_path: str = os.path.join(category_dir, f"{name}.svg")
        content_type = "image/svg+xml"
        if not os.path.exists(category_path):
            category_path: str = os.path.join(category_dir, f"{name}.png")
            content_type = "image/png"
            if not os.path.exists(category_path):
                category_path: str = os.path.join(default_category_dir, "basic_info.svg")
                content_type = "image/svg+xml"
        response = FileResponse(open(category_path, "rb"))
        response["Content-Type"] = content_type
        return response


class NewExportNameAPIView(APIView, BaseQuery):
    def get(self, request: Request, category):
        category_name = CATEGORY_TRANSLATE.get(category) or ""
        now = datetime.now()
        return build_success(
            {
                "name": f"{category_name}导出-{str(now).replace('-', '').replace(':', '').replace('.', '').replace(' ', '')}"
            }
        )


class NewExportAPIView(APIView, BaseQuery):
    _PLAYBOOK = "export"

    def post(self, request, category):
        serializer = ExportTaskRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        req: ExportRequest = serializer.save()
        if req.aql:
            asql_plus = req.aql
        else:
            asql_plus = AsqlPlusLogicalQuery.from_list(req.filters)
        query_filters = req.query_filters or []
        if req.venn_filter:
            query_filters.append(req.venn_filter)
        record_id = export_record_service.save_record(
            req.name,
            get_user(request).username,
            category,
            datetime.now() + timedelta(days=7),
            req.option.field_list,
            asql_plus,
            req.date,
            req.entity_type,
            query_filters,
        )
        workflow_id = workflow_sdk.start_workflow(self._PLAYBOOK, {"record_id": record_id})
        if workflow_id:
            export_record_service.update_workflow(record_id, workflow_id)
            export_record_service.update_progress(record_id, ExportRecordStatus.WAITING)
        else:
            export_record_service.update_progress(record_id, ExportRecordStatus.ERROR)
        return build_success({})
        # option = EntityExporterOption()
        # option.field_list = req.option.field_list
        # option.date = req.date
        # option.category = category
        # option.asql = asql_plus
        # exporter = EntityExporter()
        # file_path = exporter.export(option)
        # res = FileResponse(open(file_path, "rb"))
        # res["Content-Type"] = "application/octet-stream;charset=utf-8"
        # now = datetime.now()
        # category_name = CATEGORY_TRANSLATE.get(category, category)
        # file_name = f"{category_name}导出-{str(now)}.xlsx"
        # res["Content-Disposition"] = f'attachment;filename="{escape_uri_path(file_name)}"'
        # os.unlink(file_path)
        # return res


class ExportAPIView(APIView, BaseQuery):
    SORT_FIELDS = [{"_id": {"order": "desc"}}]

    def _generate_entites(self, req, category, date):
        # yield codecs.BOM_UTF8
        ## 获取所有的字段
        field_mapper = self.find_field_to_mapper(category)
        field_keyword_map = {}
        field_keyword_list = []
        for keyword_infos in req.childs_retrieve_info:
            field_keyword_list.append(keyword_infos.field)
            field_keyword_map[keyword_infos.field] = keyword_infos.keyword

        request_field_list = req.option.field_list or []
        ## 确定表头列需要的header
        field_header_list, field_header_ch_list = self.get_cvs_header_list(
            request_field_list=request_field_list, field_mapper=field_mapper, field_keyword_list=field_keyword_list
        )
        buffer: StringIO = StringIO()
        writer = csv.writer(buffer)
        header = field_header_ch_list
        buffer.seek(0)
        writer.writerow(header)
        buffer.seek(0)
        line = buffer.read()
        yield line.encode("utf-8")

        buffer = None
        writer = None
        wrote_count = 0
        if req.aql:
            asql_plus = req.aql
        else:
            asql_plus = AsqlPlusLogicalQuery.from_list(req.filters)
        querier = ESLogicalGroupQuerier(field_mapper)
        query_condition, high_lights, errors = querier.parse_aql(category, asql_plus, req.option, req.additions)
        if errors:
            return

        search_after = None
        request_field_list.append("asset_id")
        while True:
            source_results = entity_service.find_entity(
                category,
                date=date,
                condition=query_condition,
                fields=request_field_list,
                sort_fields=self.SORT_FIELDS,
                need_ori_response=True,
                search_after=search_after,
                limit=req.option.page_size,
                offset=0,
            )
            if not source_results:
                break
            for source_result in source_results:
                search_after = source_result["sort"]
                entity_doc = source_result["_source"] or {}
                entity_doc["_id"] = source_result["_id"]

                for key_field in field_keyword_list:
                    value = self.extract(entity_doc, key_field)
                    if not value:
                        continue
                    if field_keyword_map[key_field]:
                        results = list()
                        for row_ in value:
                            if _search(row_, field_keyword_map[key_field]):
                                results.append(row_)
                        _fields = key_field.split(".")
                        if len(_fields) == 1:
                            entity_doc[_fields] = results
                        first_field = _fields[0]
                        next_field = ".".join(_fields[1:])
                        entity_doc[first_field][next_field] = results

                header_size = len(field_header_list)
                row = [""] * header_size
                total_row = [row]

                def _is_sub_field_of_complex(field_name_):
                    field_segments = field_name_.split(".")
                    parent_name = ".".join(field_segments[:-1])
                    if parent_name in field_mapper:
                        parent_field = field_mapper[parent_name]
                        if parent_field.is_complex:
                            return True
                        else:
                            return _is_sub_field_of_complex(parent_name)
                    else:
                        return False

                ## 这里是要 进行字段搜索的   要单独列出来
                for i in range(0, len(field_header_list)):
                    field_name = field_header_list[i]
                    value = self.extract(entity_doc, field_name)
                    field = field_mapper[field_name]
                    if field.is_complex:
                        rendered = render_manager.render(value, field)
                        total_row[0][i] = rendered["rendered_value"]
                    else:
                        if isinstance(value, list):
                            if _is_sub_field_of_complex(field_name):
                                temp_field_list = []
                                child_field = field
                                for child_dict in value:
                                    if child_field is None:
                                        continue
                                    try:
                                        rendered = render_manager.render(child_dict, child_field)
                                        temp_field_list.append(rendered["rendered_value"])
                                    except Exception:
                                        logging.error(traceback.print_stack())
                                for j in range(0, len(temp_field_list)):
                                    if len(total_row) < j + 1:
                                        temp = [""] * header_size
                                        total_row.append(temp)
                                    total_row[j][i] = temp_field_list[j]
                            else:
                                child_field = field.children[0]
                                temp_field_list = []
                                for child_value in value:

                                    if child_field is None:
                                        continue
                                    try:
                                        rendered = render_manager.render(child_value, child_field)
                                        temp_field_list.append(rendered["rendered_value"])
                                    except Exception:
                                        logging.error(traceback.print_stack())
                                total_row[0][i] = ",".join(temp_field_list)
                        else:
                            dict_field = field_mapper.get(field_header_list[i])
                            if dict_field is not None:
                                rendered = render_manager.render(value, dict_field)
                                total_row[0][i] = rendered["rendered_value"]

                if buffer is None:
                    buffer: StringIO = StringIO()
                    writer = csv.writer(buffer)
                writer.writerows(tuple(total_row))
                wrote_count += 1
                if wrote_count > 100:
                    wrote_count = 0
                    buffer.seek(0)
                    line = buffer.read()
                    buffer = None
                    writer = None
                    yield line.encode("utf-8")
        if buffer:
            buffer.seek(0)
            line = buffer.read()
            yield line.encode("utf-8")

    def post(self, request: Request, category):
        value = request.data.get("value")
        try:
            params = json.loads(value)
        except Exception as e:
            return redirect("#/500")
        serializer = ExportRequestSerializer(data=params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        req: ExportRequest = serializer.save()
        res = StreamingHttpResponse(self._generate_entites(req, category, req.date))
        res["Content-Type"] = "application/octet-stream;charset=utf-8"
        now = datetime.now()
        category_name = CATEGORY_TRANSLATE.get(category, category)
        file_name = f"{category_name}导出-{str(now)}.csv"
        res["Content-Disposition"] = f'attachment;filename="{escape_uri_path(file_name)}"'
        return res

    @classmethod
    def get_cvs_header_list(cls, request_field_list=None, field_mapper=None, field_keyword_list=None):
        cvs_header_list = []
        cvs_header_ch_list = []
        if not request_field_list or not field_mapper:
            return cvs_header_list, cvs_header_ch_list
        field_keyword_list = [] if field_keyword_list is None else field_keyword_list
        for field_name in request_field_list:
            field = field_mapper.get(field_name)
            if not field:
                continue
            if field.is_complex and field.full_name in field_keyword_list:
                for child_field in field.children[0].children:
                    if child_field.full_name in cvs_header_list:
                        cvs_header_list.remove(child_field.full_name)
                        cvs_header_ch_list.remove(child_field.full_display_name)

                    if not child_field.hidden:
                        cvs_header_list.append(child_field.full_name)
                        cvs_header_ch_list.append(child_field.full_display_name)
                if field.full_name in cvs_header_list:
                    cvs_header_list.remove(field.full_name)
                    cvs_header_ch_list.remove(field.full_display_name)
            elif not field.hidden and field.full_name not in cvs_header_list:
                cvs_header_list.append(field.full_name)
                cvs_header_ch_list.append(field.full_display_name)
        return cvs_header_list, cvs_header_ch_list

    @classmethod
    def get_field_info(cls, request_field_list=None, logical_model=None):
        """
        获取字段的详细配置信息
        :param request_field_list:
        :param logical_model:
        :return:
        """
        fields = {}
        if not request_field_list or not logical_model:
            return fields
        for field_name in request_field_list:
            field = logical_model.get(field_name)
            if field is not None:
                fields[field.full_name] = field
        return fields


class JoinExportViewAPI(APIView, MetaViewBase):
    SORT_FIELDS = [{"_id": {"order": "desc"}}]

    def get_join_datas(self, category, data_ids, field_list, result):
        new_field_list = copy(field_list)
        new_field_list.append("base.entity_id")
        condition = {"bool": {"must": [{"bool": {"must": [{"terms": {"base.entity_id": data_ids}}]}}]}}
        source_results = entity_service.find_entity_loop(
            category=category, condition=condition, fields=new_field_list, date=""
        )
        if not source_results:
            return {}
        for source_result in source_results:
            asset_id = extract(source_result, "base.entity_id")
            if asset_id in result.keys():
                continue
            result[asset_id] = source_result
        return result

    @classmethod
    def get_cvs_header_list(cls, request_field_list=None, field_mapper=None):
        cvs_header_list = []
        cvs_header_ch_list = []
        if not request_field_list or not field_mapper:
            return cvs_header_list, cvs_header_ch_list

        for field_name in request_field_list:
            field = field_mapper.get(field_name)
            if not field:
                continue
            if not field.hidden and field.full_name not in cvs_header_list:
                cvs_header_list.append(field.full_name)
                cvs_header_ch_list.append(field.full_display_name)
        return cvs_header_list, cvs_header_ch_list

    def post(self, request: Request, category):
        value = request.data.get("value")
        try:
            params = json.loads(value)
        except Exception as e:
            return redirect("#/500")
        serializer = JoinExportFieldsSerializer(data=params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        user_id = get_user_id(request)
        res = StreamingHttpResponse(self._generate_entites(serializer.validated_data, category, user_id))
        res["Content-Type"] = "application/octet-stream;charset=utf-8"
        now = datetime.now()
        category_name = CATEGORY_TRANSLATE.get(category, category)
        file_name = f"主要{category_name}相关联导出-{str(now)}.csv"
        res["Content-Disposition"] = f'attachment;filename="{escape_uri_path(file_name)}"'
        return res

    def _generate_entites(self, req, category, user_id):
        """
        这里要干几个事情
        要 确定表格 表头 以及 对应的字段关联关系
        这里还要确定 要拉取哪些字段
        """
        field_header_list = {}
        field_header_ch_list = {}
        all_field_header = []
        all_field_ch_header = []

        field_mappers = {}
        ## 先确认表头
        for category_info in self.category_map[category]:
            field_mappers[category_info] = self.find_field_to_mapper(category_info)
            field_list = req.get(category_info)
            if not field_list:
                continue
            field_header_list[category_info] = []
            field_header_ch_list[category_info] = []
            category_field_mapper = self.find_field_to_mapper(category_info, date="")
            cvs_field, cvs_ch_field = self.get_cvs_header_list(field_list, category_field_mapper)
            field_header_list[category_info].extend(cvs_field)
            field_header_ch_list[category_info].extend(cvs_ch_field)
            all_field_header.extend(cvs_field)
            all_field_ch_header.extend(cvs_ch_field)
        if category not in field_header_list:
            return build_failed(ResponseCode.REQUEST_ERROR, message="漏洞实例中的字段必须选择！")
        if not field_header_list[category]:
            return build_failed(ResponseCode.REQUEST_ERROR, message="漏洞实例中的字段必须选择！")

        buffer: StringIO = StringIO()
        writer = csv.writer(buffer)
        header = all_field_ch_header
        buffer.seek(0)
        writer.writerow(header)
        buffer.seek(0)
        line = buffer.read()
        yield line.encode("utf-8")

        buffer = None
        writer = None
        wrote_count = 0

        search_after = None

        aql = req.get("aql")
        if aql:
            asql_plus = req.get("aql")
        else:
            asql_plus = AsqlPlusLogicalQuery.from_list(req.get("filters", []))

        main_category_field_mapper = self.find_field_to_mapper(category)
        querier = ESLogicalGroupQuerier(main_category_field_mapper)
        query_condition, high_lights, errors = querier.parse_aql(category, asql_plus)
        if errors:
            return
        main_need_field = copy(field_header_list[category])
        main_need_field.append("vul_instance_unique.asset")
        main_need_field.append("vul_instance_unique.vulnerability")
        count = 0
        while True:
            asset_datas_mapper = {}
            vul_dates_mapper = {}
            source_results = entity_service.find_entity(
                category,
                date="",
                condition=query_condition,
                fields=main_need_field,
                sort_fields=self.SORT_FIELDS,
                need_ori_response=True,
                search_after=search_after,
                limit=1000,
                offset=0,
            )
            if not source_results:
                break
            asset_ids = []
            vul_ids = []
            _temp_data = []
            for source_result in source_results:
                count += 1
                search_after = source_result["sort"]
                entity_doc = source_result["_source"] or {}
                entity_doc["_id"] = source_result["_id"]
                if category == Category.VUL_INSTANCE_UNIQUE:
                    asset_id = extract(entity_doc, "vul_instance_unique.asset.rel_id")
                    vul_id = extract(entity_doc, "vul_instance_unique.vulnerability.rel_id")

                    asset_ids.append(asset_id) if asset_id and asset_id not in asset_datas_mapper.keys() else ...
                    vul_ids.append(vul_id) if vul_id and vul_id not in vul_dates_mapper.keys() else ...
                    _temp_data.append(entity_doc)

            if Category.ASSET in field_header_list:
                self.get_join_datas(Category.ASSET, asset_ids, field_header_list[Category.ASSET], asset_datas_mapper)
            if Category.VUL in field_header_list:
                self.get_join_datas(Category.VUL, vul_ids, field_header_list[Category.VUL], vul_dates_mapper)

            for entity_doc in _temp_data:
                asset_id = extract(entity_doc, "vul_instance_unique.asset.rel_id")
                vul_id = extract(entity_doc, "vul_instance_unique.vulnerability.rel_id")
                if Category.ASSET in field_header_list:
                    entity_doc["asset_data"] = asset_datas_mapper.get(asset_id, {})
                if Category.VUL in field_header_list:
                    entity_doc["vul_data"] = vul_dates_mapper.get(vul_id, {})

                header_size = len(all_field_header)
                row = [""] * header_size
                n = 0
                row = [row]
                for category_info, field_list in field_header_list.items():
                    field_mapper = field_mappers[category_info]

                    for i in range(0, len(field_list)):
                        key = None

                        if category_info == category:
                            key = field_list[i]
                        elif category_info == Category.ASSET:
                            key = f"asset_data.{field_list[i]}"
                        elif category_info == Category.VUL:
                            key = f"vul_data.{field_list[i]}"

                        if not key:
                            continue
                        value = self.extract(entity_doc, key) or ""
                        dict_field = field_mapper.get(field_list[i])
                        if dict_field is not None:
                            rendered = render_manager.render(value, dict_field)
                            row[0][i + n] = rendered["rendered_value"]
                    n = n + len(field_list)

                if buffer is None:
                    buffer: StringIO = StringIO()
                    writer = csv.writer(buffer)
                writer.writerows(tuple(row))
                wrote_count += 1
                if wrote_count > 100:
                    wrote_count = 0
                    buffer.seek(0)
                    line = buffer.read()
                    buffer = None
                    writer = None
                    yield line.encode("utf-8")

        if buffer:
            buffer.seek(0)
            line = buffer.read()
            yield line.encode("utf-8")


class JoinExportTableViewAPI(JoinExportViewAPI):
    @classmethod
    def get_cvs_header_list(cls, request_field_list=None, field_mapper=None, category=None):
        cvs_header_list = []
        header_temp_en_list = []
        header_temp_ch_list = []
        if not request_field_list or not field_mapper:
            return header_temp_en_list, header_temp_ch_list, cvs_header_list

        for field_name in request_field_list:
            field = field_mapper.get(field_name)
            if not field:
                continue
            if not field.hidden and field.full_name not in header_temp_en_list:
                header_temp_en_list.append(field.full_name)
                if category == Category.ASSET:
                    cvs_header_list.append(
                        {"title": f"资产-{field.full_display_name}", "key": f"asset_data.{field.full_name}"}
                    )
                elif category == Category.VUL:
                    cvs_header_list.append(
                        {"title": f"情报-{field.full_display_name}", "key": f"vul_data.{field.full_name}"}
                    )
                else:
                    cvs_header_list.append({"title": field.full_display_name, "key": field.full_name})
                header_temp_ch_list.append(field.full_display_name)
        return header_temp_en_list, header_temp_ch_list, cvs_header_list

    def post(self, request: Request, category):
        serializer = JoinExportFieldsSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        user_id = get_user_id(request)
        field_header_list = {}
        field_header_ch_list = {}
        all_field_header = []
        all_ch_field_header = []
        cvs_header = []
        ## 先确认表头
        validated_data = serializer.validated_data
        data_asset = {}
        data_vul = {}
        field_mappers = {}
        data_vul_unique_field_names = {"enable": True, "field_names": validated_data.get("vul_instance_unique")}
        if "asset" in validated_data:
            data_asset = {"enable": True, "field_names": validated_data.get("asset")}
        if "vul" in validated_data:
            data_vul = {"enable": True, "field_names": validated_data.get("vul")}
        join_export_fields_service.save_vul_export_fields(
            user_id=user_id,
            main_category=category,
            vul_unique_field_names=data_vul_unique_field_names,
            asset_field_names=data_asset,
            vul_field_names=data_vul,
        )

        for category_info in self.category_map[category]:
            field_list = validated_data.get(category_info)
            field_mappers[category_info] = self.find_field_to_mapper(category_info)
            if not field_list:
                continue
            field_header_list[category_info] = []
            field_header_ch_list[category_info] = []
            category_field_mapper = self.find_field_to_mapper(category_info, date="")
            cvs_field, cvs_ch_field, cvs_dict_header = self.get_cvs_header_list(
                field_list, category_field_mapper, category_info
            )
            field_header_list[category_info].extend(cvs_field)
            field_header_ch_list[category_info].extend(cvs_ch_field)
            all_field_header.extend(cvs_field)
            cvs_header.extend(cvs_dict_header)

        main_category_field_mapper = self.find_field_to_mapper(category)
        querier = ESLogicalGroupQuerier(main_category_field_mapper)
        query_condition, high_lights, errors = querier.parse_aql(category, validated_data.get("aql"))
        if errors:
            return
        if category not in field_header_list:
            return build_failed(ResponseCode.REQUEST_ERROR, message="漏洞实例中的字段必须选择！")
        if not field_header_list[category]:
            return build_failed(ResponseCode.REQUEST_ERROR, message="漏洞实例中的字段必须选择！")
        main_need_field = field_header_list[category]
        main_need_field.append("vul_instance_unique.asset")
        main_need_field.append("vul_instance_unique.vulnerability")
        table_value = []
        asset_datas_mapper = {}
        vul_dates_mapper = {}
        source_results = entity_service.find_entity(
            category,
            date="",
            condition=query_condition,
            fields=main_need_field,
            sort_fields=self.SORT_FIELDS,
            need_ori_response=True,
            limit=20,
            offset=0,
        )
        if not source_results:
            return build_success(data={"table_header": cvs_header, "raw": []})
        asset_ids = []
        vul_ids = []
        _temp_data = []
        for source_result in source_results:
            row_data = {}
            entity_doc = source_result["_source"] or {}
            entity_doc["_id"] = source_result["_id"]
            if category == Category.VUL_INSTANCE_UNIQUE:
                asset_id = extract(entity_doc, "vul_instance_unique.asset.rel_id")
                vul_id = extract(entity_doc, "vul_instance_unique.vulnerability.rel_id")
                asset_ids.append(asset_id) if asset_id and asset_id not in asset_datas_mapper.keys() else ...
                vul_ids.append(vul_id) if vul_id and vul_id not in vul_dates_mapper.keys() else ...
                _temp_data.append(entity_doc)

            self.get_join_datas(Category.ASSET, asset_ids, field_header_list[Category.ASSET], asset_datas_mapper)
            self.get_join_datas(Category.VUL, vul_ids, field_header_list[Category.VUL], vul_dates_mapper)

            for entity_doc in _temp_data:
                asset_id = extract(entity_doc, "vul_instance_unique.asset.rel_id")
                vul_id = extract(entity_doc, "vul_instance_unique.vulnerability.rel_id")
                if Category.ASSET in field_header_list:
                    entity_doc["asset_data"] = asset_datas_mapper.get(asset_id, {})
                if Category.VUL in field_header_list:
                    entity_doc["vul_data"] = vul_dates_mapper.get(vul_id, {})

                for category_info, field_list in field_header_list.items():
                    field_mapper = field_mappers[category_info]
                    for i in range(0, len(field_list)):
                        key = None

                        if category_info == category:
                            key = field_list[i]
                        elif category_info == Category.ASSET:
                            key = f"asset_data.{field_list[i]}"
                        elif category_info == Category.VUL:
                            key = f"vul_data.{field_list[i]}"

                        if not key:
                            continue
                        value = self.extract(entity_doc, key)
                        dict_field = field_mapper.get(field_list[i])
                        if dict_field is not None:
                            rendered = render_manager.render(value, dict_field)
                            row_data[key] = rendered["rendered_value"]
            table_value.append(row_data)

        data = {"table_header": cvs_header, "raw": table_value}
        return build_success(data=data)


class EntityCategoryDetailAPI(APIView, BaseQuery):
    def post(self, request, category):
        serializer = EntityCategoryDetailRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        req = serializer.save()
        meta_view = self.get_meta_view(category)
        if not meta_view:
            return build_failed(ResponseCode.INVALID_DATA)

        entity_data = entity_service.get_entity(category, meta_view.pk_field, req.entity_id, date=req.date)

        if entity_data is None:
            return build_failed(ResponseCode.NOT_FOUND)
        meta_asset_type_view = self.get_asset_type_view(category, entity_data)
        meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, req.date)

        setting = {"meta_models": meta_models, "meta_field_mapper": meta_field_mapper}
        result = render_manager.get_card_result(entity_data, CardType.FABRIC, meta_asset_type_view, setting=setting)
        return build_success(result)


class EntityCardAPI(EntityDetailCommonAPI):
    _fields = ["support_cards"]

    def get(self, request, category):
        my_response, entity_data, validated_data = self.handle(category, request.query_params)
        if my_response:
            return my_response

        asset_type = self.get_asset_type(entity_data)
        if not asset_type:
            return build_failed(ResponseCode.INVALID_DATA)

        _view = self.get_asset_type_view(category, entity_data)
        support_cards = _view.support_cards if _view else [{"card_type": "fabric"}]
        _result = EntitySupportCardSerializer(instance=support_cards, many=True).data
        _result = {"asset_type": asset_type, "support_cards": _result}
        return build_success(_result)


class EntityTitleAPI(APIView, BaseQuery):
    _default_title_expr = ["base.name"]

    def get(self, request, category):
        serializer = EntitySupportCardRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        result = ""
        validated_data = serializer.validated_data

        entity_id = validated_data["entityId"]
        date = validated_data["date"]

        meta_view = self.get_meta_view(category)
        if not meta_view:
            return build_success(result)

        entity_data = entity_service.get_entity(category, meta_view.pk_field, entity_id, date=date)
        if not entity_data:
            return build_success(result)

        meta_asset_type_view = self.get_asset_type_view(category, entity_data)
        multi_title_expr = meta_asset_type_view.multi_title_expr if meta_asset_type_view else self._default_title_expr

        values = []
        for field in multi_title_expr:
            value = self.extract(entity_data, field)
            if value is not None:
                values.append(value)

        result = "|".join(map(str, values))
        return build_success(result)


class EntityLogoAPI(APIView, BaseQuery):
    def get(self, request, category):
        serializer = EntitySupportCardRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        result = ""
        validated_data = serializer.validated_data

        entity_id = validated_data["entityId"]
        date = validated_data["date"]

        meta_view = self.get_meta_view(category)
        if not meta_view:
            return build_success(result)

        entity_data = entity_service.get_entity(category, meta_view.pk_field, entity_id, date=date)
        if not entity_data:
            return build_success(result)

        meta_asset_type_view = self.get_asset_type_view(category, entity_data)
        asset_type_entity: AssetType = asset_type_service.get_asset_type(name=meta_asset_type_view.asset_type)
        meta_mode_entity: MetaModel = meta_model_service.get_meta_model(asset_type_entity.model_id)
        if meta_mode_entity and meta_mode_entity.logo_id:
            logo_file = asset_type_service.get_file(file_id=meta_mode_entity.logo_id)
            result = generate_image_data(logo_file.read())
        return build_success(result)


class EntityCardDetailAPI(EntityDetailCommonAPI):
    default_serializer = EntityCardDetailRequestSerializer
    entity_id_name = "entity_id"

    def post(self, request, category):
        my_response, entity_data, validated_data = self.handle(category, request.data)
        if my_response:
            return my_response
        _card_type = validated_data.get("card_type")
        _card_name = validated_data.get("card_name")
        _setting = validated_data.get("setting")
        _date = validated_data.get("date")
        _asql = validated_data.get("asql")
        _result = None

        from caasm_service.entity.meta_view import MetaAssetTypeViewEntity

        category = category.split(".")[-1]

        _meta_asset_type_view: MetaAssetTypeViewEntity = self.get_asset_type_view(category, entity_data)
        card_setting = _meta_asset_type_view
        if _card_name:
            for card in _meta_asset_type_view.support_cards:
                if card.card_name == _card_name:
                    card_setting = card
                    break
        _setting["meta_field_mapper"] = self.find_field_to_mapper(category)
        _result = render_manager.get_card_result(
            entity_data, category, _date, _card_type, _meta_asset_type_view, card_setting, _setting, _asql
        )
        return build_success(_result)


class EntityFabricMenuAPI(APIView, BaseQuery):
    def get(self, request, category):
        serializer = EntitySupportCardRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.INVALID_DATA, message=serializer)

        validated_data = serializer.save()

        entity_id = validated_data["entityId"]
        date = validated_data["date"]
        entity_data = self.get_entity_data(category, date, entity_id)

        if not entity_data:
            return build_failed(ResponseCode.NOT_FOUND)
        category = category.split(".")[-1]
        meta_asset_type_view = self.get_asset_type_view(category, entity_data)
        meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, date)
        total_fields = []
        for model_id, tmp_fields in meta_field_mapper.items():
            total_fields.extend(tmp_fields)
        field_mapper = {i.full_name: i for i in self.open_up_field(total_fields)}

        trees = FabricCardHandler().get_tree(
            entity_data, meta_models, meta_field_mapper, field_mapper, meta_asset_type_view
        )

        return build_success(self._clean_tree(trees))

    @classmethod
    def _clean_tree(cls, trees, result=None):
        if not trees:
            return []

        if not result:
            result = []
        for tree in trees:
            children = tree.children
            name = tree.name
            display_name = tree.display_name

            tmp_result = {"name": name, "display_name": display_name, "children": cls._clean_tree(children)}
            result.append(tmp_result)
        return result


class EntityAdapterMenuAPI(EntityDetailCommonAPI):
    _default_fields = ["name", "display_name"]

    def get(self, request, category):
        handle_response, entity_data, validated_data = self.handle(category, request.query_params)
        if handle_response:
            return handle_response

        adapter_names = AdapterCardHandler().find_adapter_name(entity_data)
        if not adapter_names:
            return build_success([])

        adapters = adapter_service.find_adapter(names=adapter_names, fields=self._default_fields)

        data = EntityAdapterMenuResponseSerializer(instance=adapters, many=True).data
        return build_success(data)


class AqlFieldGroupAPI(APIView):
    _IGNORE_DATA_TYPES = [MetaFieldType.RELATION, MetaFieldType.OBJECT]
    _ASC_TYPES = {
        MetaFieldType.IP,
        MetaFieldType.IPV6,
        MetaFieldType.IPV4,
        MetaFieldType.IP,
        MetaFieldType.INT,
        MetaFieldType.FLOAT,
    }
    _GT_TYPES = {
        MetaFieldType.INT,
        MetaFieldType.FLOAT,
        MetaFieldType,
        MetaFieldType.IPV4,
        MetaFieldType.IPV6,
        MetaFieldType.IP,
    }

    def __init__(self, *args, **kwargs):
        super(AqlFieldGroupAPI, self).__init__(*args, **kwargs)
        self._keyword_build_mapper = {
            MetaFieldType.FLOAT: self._gte_keyword_build,
            MetaFieldType.INT: self._gte_keyword_build,
            MetaFieldType.DATE: self._gte_keyword_build,
            MetaFieldType.DATETIME: self._gte_keyword_build,
            MetaFieldType.TIMESTAMP: self._gte_keyword_build,
            MetaFieldType.IP: self._equal_keyword_build,
            MetaFieldType.IPV4: self._equal_keyword_build,
            MetaFieldType.LIST: self._equal_keyword_build,
        }
        self._convert_agg_name_mapper = {
            MetaFieldType.VERSION: self._convert_agg_version,
            MetaFieldType.ENUM: self._convert_agg_enum,
        }

    def post(self, request, category):
        # 基础校验
        serializer = AqlFieldValueGroupRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        # 获取请求参数
        validated_data = serializer.save()
        if category == "ownership":
            category = Category.ASSET
            ownership_type = validated_data.get("ownership_type")
            field_name = validated_data.get("field_name")
            field_name = f"asset_base.{MAPPER_ATTRI_TO_OWNERSHIP[ownership_type]}.{field_name}"
        else:
            field_name = validated_data.get("field_name")
        aql = validated_data.get("aql")
        additions = validated_data.get("additions")
        keyword = validated_data.get("keyword")
        max_fetch = validated_data.get("max_fetch")

        meta_field_mapper = render_manager.default_query.find_field_to_mapper(category)
        if not category:
            return build_failed(ResponseCode.REQUEST_ERROR, message="请求参数无效")
        meta_field = meta_field_mapper.get(field_name)
        if not meta_field:
            return build_failed(ResponseCode.REQUEST_ERROR, message="无效的字段信息")

        # 业务校验
        meta_field_type = meta_field.type
        if meta_field_type in self._IGNORE_DATA_TYPES:
            return build_success([])

        keyword_aql = self._build_keyword_to_aql(meta_field, keyword)
        data_filter = generate_data_filter(request, category)
        if data_filter:
            data_aql = AsqlPlusLogicalQuery.from_list([data_filter])
            aql = data_aql if not aql else data_aql + ";" + aql
        aql = keyword_aql if not aql else aql + ";" + keyword_aql
        querier = ESLogicalGroupQuerier(meta_field_mapper)
        condition, _, errors = querier.parse_aql(category, aql, additions=additions)
        if errors:
            error_msg = "，".join(errors) if isinstance(errors, list) else errors
            return build_failed(ResponseCode.REQUEST_ERROR, message=error_msg)

        # # 业务逻辑
        table = entity_service.get_table(category)
        aggs = self._build_agg(meta_field, max_fetch, keyword)
        query_params = {"aggs": aggs, "_source_includes": [field_name]}
        _result = es_handler.search(table, query=condition, **query_params)
        data = self._clean_result(_result, meta_field, keyword)

        return build_success(data)

    def _build_agg(self, meta_field, max_fetch, keyword):
        if meta_field.complex_full_name:
            return self._build_agg_complex(meta_field, max_fetch, keyword)
        return self._build_agg_simple(meta_field, max_fetch, keyword)

    def _build_keyword_to_aql(self, meta_field, keyword):
        if not keyword:
            return ""
        meta_field_type = meta_field.type
        _build_method = self._keyword_build_mapper.get(meta_field_type) or self._default_keyword_build
        _result = _build_method(meta_field.full_name, keyword)
        return "$." + _result

    @classmethod
    def _equal_keyword_build(cls, meta_field_name, keyword):
        return f"{meta_field_name} = '{keyword}'"

    @classmethod
    def _gte_keyword_build(cls, meta_field_name, keyword):
        return f"{meta_field_name} >= {keyword}"

    @classmethod
    def _default_keyword_build(cls, meta_field_name, keyword):
        return f"{meta_field_name}.regex('{keyword}')"

    def _convert_agg(self, meta_field):
        meta_field_type = meta_field.type
        _convert_agg_method = self._convert_agg_name_mapper.get(meta_field_type) or self._convert_agg_default
        return _convert_agg_method(meta_field.full_name)

    @classmethod
    def _convert_agg_default(cls, meta_field_full_name):
        return meta_field_full_name

    @classmethod
    def _convert_agg_version(cls, meta_field_full_name):
        return meta_field_full_name + "." + "plain"

    @classmethod
    def _convert_agg_enum(cls, meta_field_full_name):
        return meta_field_full_name + "." + "text"

    def _get_filter(self, meta_field, keyword):
        _filter_mapping = {
            MetaFieldType.IP: self._get_ip_filter,
            MetaFieldType.IPV4: self._get_ip_filter,
            MetaFieldType.IPV6: self._get_ip_filter,
            MetaFieldType.STRING: self._get_str_filter,
            MetaFieldType.VERSION: self._get_str_filter,
        }
        filter_func = _filter_mapping.get(meta_field.type, self._get_common_filter)
        return filter_func(meta_field.type, self._convert_agg(meta_field), keyword)

    def _build_agg_simple(self, meta_field, max_fetch, keyword):
        sort_type = "asc" if meta_field.type in self._ASC_TYPES else "desc"
        filter_ = self._get_filter(meta_field, keyword)
        if filter_ is None:
            result = {
                "count": {
                    "terms": {
                        "field": self._convert_agg(meta_field),
                        "order": [{"_key": sort_type}],
                        "size": max_fetch,
                    },
                    "aggs": {},
                }
            }
        else:
            result = {
                "count": {
                    "filter": filter_,
                    "aggs": {
                        "statistics": {
                            "terms": {"field": meta_field.full_name, "size": max_fetch, "order": [{"_key": sort_type}]}
                        }
                    },
                }
            }
        return result

    def _build_agg_complex(self, meta_field, max_fetch, keyword):
        sort_type = "asc" if meta_field.type in self._ASC_TYPES else "desc"
        filter_ = self._get_filter(meta_field, keyword)
        if filter_ is None:
            return {
                "statistics": {
                    "nested": {"path": meta_field.complex_full_name},
                    "aggs": {
                        "count": {
                            "terms": {
                                "field": self._convert_agg(meta_field),
                                "size": max_fetch,
                                "order": [{"_key": sort_type}],
                            },
                            "aggs": {"DISTINCT": {"cardinality": {"field": self._convert_agg(meta_field)}}},
                        }
                    },
                }
            }
        else:
            return {
                "statistics": {
                    "nested": {"path": meta_field.complex_full_name},
                    "aggs": {
                        "count": {
                            "filter": filter_,
                            "aggs": {
                                "statistics": {
                                    "terms": {
                                        "field": self._convert_agg(meta_field),
                                        "size": max_fetch,
                                        "order": [{"_key": sort_type}],
                                    },
                                    "aggs": {"DISTINCT": {"cardinality": {"field": self._convert_agg(meta_field)}}},
                                }
                            },
                        }
                    },
                }
            }

    def _get_common_filter(self, field_type: MetaFieldType, field_name: str, keyword):
        if not keyword:
            return None
        compare_type = "gte" if field_type in self._GT_TYPES else "lte"
        return {"range": {field_name: {compare_type: keyword}}}

    def _get_ip_filter(self, _, field_name: str, keyword: str):
        if keyword is None:
            keyword = ""
        if not isinstance(keyword, str):
            keyword = str(keyword)
        if not keyword:
            return None
        while True:
            if keyword.endswith("."):
                keyword = keyword[:-1]
            else:
                break
        try:
            from IPy import IP

            ip = IP(keyword)
            ip_keyword = str(ip)
            if not ip_keyword.startswith(keyword):
                ip_keyword = ""
        except ValueError:
            ip_keyword = ""
        if not ip_keyword:
            ip_keyword = "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"
        return {"range": {field_name: {"gte": ip_keyword}}}

    def _get_str_filter(self, _, field_name: str, keyword: str):
        if keyword:
            return {"match_bool_prefix": {field_name: keyword}}
        else:
            return None

    @classmethod
    def _clean_result(cls, data, meta_field, keyword):
        result = []
        aggregations = data.get("aggregations", {})
        if meta_field.complex_full_name:
            count_statistics = aggregations.get("statistics", {}).get("count", {})
        else:
            count_statistics = aggregations.get("count", {})
        if "statistics" in count_statistics:
            count_statistics = count_statistics.get("statistics", {})

        buckets = count_statistics.get("buckets", [])
        for bucket in buckets:
            bucket_key = cls._get_key_result(meta_field, bucket)
            if keyword is not None and not str(bucket_key).startswith(keyword):
                continue
            count = bucket.get("doc_count", 0)
            result.append({"label": bucket_key, "value": bucket_key, "count": count})
        return result

    @classmethod
    def _get_key_result(cls, meta_field, bucket):
        meta_field_type = meta_field.type

        if meta_field_type in [MetaFieldType.DATE, MetaFieldType.DATETIME]:
            return bucket.get("key_as_string")

        return bucket.get("key")


class OwnershipFieldGroupAPI(AqlFieldGroupAPI):

    serializer_class = OwnershipFieldGroupRequestSerializer


class UpdateUserDefaultEntityFieldsAPIView(APIView):
    def post(self, request: Request, category):
        serializer = UpdateUserDefaultEntityFieldsRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: UserDefaultEntityFieldsRequest = serializer.save()
            user_default_entity_fields_service.save_default_fields(
                get_user_id(request),
                category,
                req.field_names,
                req.entity_type,
            )
            return build_success({})
        return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)


class UpdateSystemDefaultEntityFieldsAPIView(APIView):
    def post(self, request: Request, category: str):
        serializer = EntityFavorityTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        entity_type = serializer.validated_data.get("entity_type", None)
        user_default_entity_fields_service.delete_default_fields(get_user_id(request), category, entity_type)
        entity_view: EntityViewEntity = entity_view_service.get_view(category, entity_type)
        if entity_view is None:
            entity_view = entity_view_service.get_view(category, None)
            if not entity_view:
                entity_view = entity_view_service.get_view(None, None)
        default_fields = entity_view.default_fields
        return build_success(default_fields)


class MetaEntityFieldsAPIView(APIView):
    def get(self, request: Request, category: str):
        field_names_list: List[MetaEntityFields] = meta_entity_fields_service.find_entity_fields_list(category)
        return build_success(EntityFieldsSerializer(instance=field_names_list, many=True).data)


class FavoriteEntityFieldsAPIView(APIView):
    def get(self, request: Request, category: str):
        serializer = EntityFavorityTypeSerializer(data=request.query_params)
        if serializer.is_valid():
            entity_type = serializer.validated_data.get("entity_type", None)
            user_default_fields: UserEntityDefaultFields = user_default_entity_fields_service.get_default_fields(
                get_user_id(request),
                category,
                entity_type,
            )
            if user_default_fields:
                field_names = user_default_fields.field_names
            else:
                meta_view = meta_view_service.get_view(category)
                field_names = meta_view.default_fields
            return build_success(data=field_names)
        else:
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)


class MetaEntityTypeFieldsAPIView(APIView):
    def post(self, request: Request, category: str):
        serializer = EntityTypeSerializer(data=request.data)
        if serializer.is_valid():
            entity_type = serializer.save()
            entity_type_view: MetaEntityTypeViewEntity = meta_entity_type_service.get_entity_type_view(
                category, entity_type
            )
            if entity_type_view:
                return build_success({"default_fields": entity_type_view.default_fields})
            return build_success({"default_fields": ["base.entity_id"]})
        return build_failed(-1, str(serializer.errors))


class SceneViewAPIView(EntityTypeViewBase, APIView):
    #   请求大类基本显示配置
    def post(self, request: Request, category: str):
        serializer = CategoryRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: SceneViewRequest = serializer.save()
            if category == Category.TOTAL_ASSET:
                category = Category.ASSET
            result = self._get_view(category, req.date, req.entity_type)
            user_default_fields: UserEntityDefaultFields = user_default_entity_fields_service.get_default_fields(
                get_user_id(request),
                category,
                req.entity_type,
            )
            if user_default_fields:
                result["default_fields"] = user_default_fields.field_names
            result["entity_type_value"] = ENTITY_TYPE_NONE_VALUE
            res_serializer = SceneViewConfigResponseSerializer(instance=result)
            return build_success(res_serializer.data)
        return build_failed(-1, str(serializer.errors))


class EntityTypeViewAPIView(EntityTypeViewBase, APIView):
    #   请求实体类型（资产类型）变化后的字段等设定
    def post(self, request: Request, category: str):
        serializer = EntityTypeViewRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: EntityTypeViewConfigRequest = serializer.save()
            result = self._get_view(category, req.date, req.entity_type)
            user_default_fields: UserEntityDefaultFields = user_default_entity_fields_service.get_default_fields(
                get_user_id(request),
                category,
                req.entity_type,
            )
            if user_default_fields:
                result["default_fields"] = user_default_fields.field_names
            return build_success(EntityTypeViewResponseSerializer(instance=result).data)
        return build_failed(-1, str(serializer.errors))


class QueryFilterViewAPIView(QueryFilterRenderBase, BaseQuery, APIView):
    def post(self, request: Request, category: str):
        serializer = GetQueryFilterRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: QueryFilterRequest = serializer.save()
            global_asql = self._get_asql(req.asql, category, req.date, req.filters, req.entity_type)
            query_filters = []
            if req.venn_filter:
                query_filters.append(req.venn_filter)
            query_filters, venn_filter = self._calculate_query_filters(
                query_filters, global_asql, category, req.date, req.entity_type, init=False
            )
            return build_success(
                GetQueryFilterResponseSerializer(
                    instance={"query_filters": query_filters, "venn_filter": venn_filter}
                ).data
            )
        return build_failed(-1, str(serializer.errors))


class EntityTypesAPIView(EntityTypeViewBase, APIView):
    def get(self, request: Request, category: str):
        serializer = SnapshotDateSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.INVALID_TIMESTAMP)
        date = serializer.validated_data.get("date")
        entity_types = self._get_entity_types(category, date)
        return build_success(data=entity_types)


class FilterAttributeConfigAPIView(EntityTypeViewBase, APIView):
    def get(self, request: Request, category: str):
        filter_attributes = self.get_default_attributes(category)
        return build_success(data=[filter_query.as_dict() for filter_query in filter_attributes])


class QueryAttributeAPIView(QueryFilterRenderBase, BaseQuery, APIView):
    def post(self, request: Request, category: str):
        serializer = QueryAttributeRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        req: QueryAttributeRequest = serializer.save()
        global_asql = self._get_asql(req.asql, req.category, req.date, req.filters, req.entity_type)
        query_filters = []
        query_filters.append(req.attribute_filter)
        if req.venn_filter:
            query_filters.append(req.venn_filter)
        filter_result = query_filter_manager.calculate_attribute_filter(
            global_asql,
            category,
            req.date,
            venn_filter_entity=req.venn_filter,
            attribute_filter_entity=req.attribute_filter,
            entity_type=req.entity_type,
            init=False,
            offset=req.page_size * req.page_index,
            limit=req.page_size,
            query_filter_id=req.query_filter_id,
        )
        result = filter_result.as_dict()
        result["count"] = 5000
        return build_success(data=result)


class FavoriteQueryApiView(AqlQueryAPI, EntityTypeViewBase):
    def post(self, request: Request, category: str):
        serialize = FavoriteAttributeRequest(data=request.data)
        if not serialize.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serialize.errors)
        name = serialize.validated_data["name"]
        entities = user_favorite_service.get_favorites(name=name, category=category)
        if len(entities) == 0:
            return build_failed(code=ResponseCode.NOT_FOUND, message="没有找到收藏")
        favorite_entity: UserFavoriteEntity = entities[0]
        query_data = self._get_view(
            category=category,
            date=None,
            entity_type=favorite_entity.entity_type,
            query_filters=favorite_entity.query_filters,
            asql=favorite_entity.asql,
            asql_type=favorite_entity.asql_type,
            default_fields=favorite_entity.default_fields,
        )
        if isinstance(request.data, QueryDict):
            request.data._mutable = True
        # 查询语句处理
        filter_queries = []
        if query_data.get("asql"):
            resolver = AsqlPlusResolver(as_fulltext=True)
            try:
                query = resolver.resolve_logic_query(query_data.get("asql"))
            except ValueError as exc:
                return build_failed(-1, str(exc))
            if query is not None:
                field_mapper = self.find_field_to_mapper(category)
                filter_queries = [q.to_dict(field_mapper) for q in query.queries]
                for filter_query in filter_queries:
                    for key, value in filter_query.items():
                        if isinstance(value, AsqlType):
                            filter_query[key] = value.value
                        else:
                            filter_query[key] = value
                request.data["filters"] = filter_queries
        request.data["asql"] = ""
        request.data["entity_type"] = query_data.get("entity_type", None)
        request.data["asql_type"] = query_data.get("asql_type", None)
        request.data["option"] = {
            "field_list": query_data.get("default_fields", None),
        }

        # 维恩图过滤
        venn_filter = query_data.get("venn_filter", None)
        if isinstance(venn_filter, QueryFilterEntity):
            request.data["venn_filter"] = venn_filter.as_dict()
        return super().post(request, category)
