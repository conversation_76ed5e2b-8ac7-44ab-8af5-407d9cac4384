from enum import Enum
from caasm_tool.constants import StrEnum


class VulSeverity(Enum):
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4
    NONE = 5


VUL_SEVERITY_MAPPING = {
    VulSeverity.CRITICAL: "严重",
    VulSeverity.HIGH: "高危",
    VulSeverity.MEDIUM: "中危",
    VulSeverity.LOW: "低危",
    VulSeverity.NONE: "无",
}


class VulAttention(Enum):
    #   监管下发
    REGULATOR = 1
    #   威胁情报热点
    HOTSPOT = 2


VUL_ATTENTION_MAPPING = {VulAttention.REGULATOR: "监管下发", VulAttention.HOTSPOT: "威胁情报热点"}


class FlawType(Enum):
    SECURITY = 1
    DATABASE = 2
    OS = 3


FLAW_TYPE_MAPPING = {FlawType.SECURITY: "信息安全产品", FlawType.DATABASE: "数据库", FlawType.OS: "操作系统"}


class FlawMethod(Enum):
    SELF = 1
    VENDOR = 2


FLAW_METHOD_MAPPING = {FlawMethod.SELF: "自主发现", FlawMethod.VENDOR: "生产厂家主动提示"}


class FlawStatus(Enum):
    UNSOLVED = 1
    SOLVED = 2


FLAW_STATUS_MAPPING = {FlawStatus.SOLVED: "已解决", FlawStatus.UNSOLVED: "未解决"}


class VulStatus(Enum):
    NEW = 1
    ACTIVE = 2
    ONLINE = 3
    OFFLINE = 4
    RECURRENT = 5
    DISAPPEARED = 6


VUL_STATUS_MAPPING = {
    VulStatus.NEW: "新增",
    VulStatus.ACTIVE: "活动",
    VulStatus.ONLINE: "资产上线",
    VulStatus.OFFLINE: "资产下线",
    VulStatus.RECURRENT: "复现",
    VulStatus.DISAPPEARED: "消亡",
}


class VulResponseStatus(Enum):
    ACTIVE = 1
    CONFIRMED = 2
    REMEDIATING = 3
    FIXED = 4
    MITIGATED = 5
    ACCEPTED = 6
    TOLERABLE = 7


VUL_RESPONSE_STATUS_MAPPING = {
    VulResponseStatus.ACTIVE: "活动",
    VulResponseStatus.CONFIRMED: "已确认",
    VulResponseStatus.REMEDIATING: "处置中",
    VulResponseStatus.FIXED: "已修复",
    VulResponseStatus.MITIGATED: "已缓解",
    VulResponseStatus.ACCEPTED: "已验收",
    VulResponseStatus.TOLERABLE: "无需修复",
}


class VulnTraitName(StrEnum):
    SQL_INJECTION = "SQL注入"
    Execution = ""
    XSS = "跨站脚本"
    COMMAND_INJECTION = "命令注入"
    BUFFER_OVERFLOW = "缓冲区溢出"
    PRIVILEGE_ESCALATION = "权限提升"
    INFO_LEAKAGE = "信息泄露"
    DOS = "拒绝服务"
    FILE_INCLUSION = "文件包含"
    PATH_TRAVERSAL = "路径遍历"
