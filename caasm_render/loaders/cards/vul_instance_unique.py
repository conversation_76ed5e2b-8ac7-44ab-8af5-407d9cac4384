import logging

# import traceback
from typing import List
from cvss import CVSS3, CVSS2
from nanoid import generate

# from pyorient import OrientRecord
from caasm_service_base.entity.adapter import Adapter

# from caasm_aql.tool import parse_aql

# from caasm_businesses.enums import GRADE_PROTECTION_LEVEL_MAPPING, MAGNITUDE_MAPPING, CRITICAL_INFRASTRUCTURE_MAPPER
from caasm_aql.queriers.es import ESLogicalGroupQuerier
from caasm_meta_data.constants import Category
from caasm_render.loaders.cards.base import CardBaseHandler
from caasm_render.loaders.constants import CardType
from caasm_service.runtime import (
    entity_service,
    adapter_instance_service,
    adapter_service,
    business_portraiter_service,
    setting_service,
)

from caasm_service.entity.adapter_instance import AdapterInstance
from caasm_service.entity.business_poraiter import BusinessPortrait
from caasm_service.constants.vuln import CVSSSecurityNameMapper, CVSSSecurity
from caasm_service.constants.setting import SettingName


log = logging.getLogger()


class VulInstanceUniqueCardHandler(CardBaseHandler):
    _asset_addr_ip_field = "network.priority_addr"
    _asset_vul_cve_id_field = "vul_instance_unique.cve_id"
    _asset_vul_asset_field = "vul_instance_unique.asset"
    _vul_instance_unique_traits_field = "vul_instance_unique.traits"
    _vul_instance_field_mapper = {
        "sources": "vul_instance_unique.source",
        "name": "vul_instance_unique.name",
        "status": "vul_instance_unique.status",
        "create_datetime": "vul_instance_unique.create_date",
        "repair_datetime": "vul_instance_unique.repair_date",
        "severity": "vul_instance_unique.severity",
        "priority_score": "vul_instance_unique.priority_score",
        "remediation_severity": "vul_instance_unique.remediation_severity",
        "priority_hits": "vul_instance_unique.priority_hits",
        "cve_id": _asset_vul_cve_id_field,
        "description": "vul_instance_unique.description",
        "priority": "vul_instance_unique.priority",
        "adapter_sources": "base.adapters",
    }

    _vul_cve_id_field = "vul.cve_id"
    _vul_entity_id_field = "base.entity_id"
    _vul_cvss3_base_score_field = "vul.cvss_3.base_score"
    _vul_cvss2_base_score_field = "vul.cvss_2.base_score"
    _vul_exploits_field = "vul.exploits"
    _vul_cwes_field = "vul.cwes"
    _vul_cpe_field = "vul.cpe"
    _vul_traits_field = "vul.traits"
    _vul_cvss_3_field = "vul.cvss_3"
    _vul_cvss_2_field = "vul.cvss_2"
    _vul_field_mapper = {
        "cve": "vul.cve_id",
        "cnvd": "vul.cnvd_id",
        "cnnvd": "vul.cnnvd_id",
        "published": "vul.published",
        "last_modified": "vul.last_modified",
        "vendor_advisory": "vul.vendor_advisory",
        "description": "vul.description",
        "solution": "vul.solution",
        "exploits": _vul_exploits_field,
        "is_poc": "vul.is_poc",
        "is_exp": "vul.is_exp",
        "poc": "vul.poc",
    }
    _vul_basic_field_mapper = {
        "is_solution": "vul.is_solution",
        "is_exploit": "vul.is_exploit",
    }

    _asset_id_field = "base.entity_id"
    _asset_entity_type_field = "base.entity_type"
    _asset_field_mapper = {
        "asset_ip": "network.priority_addr",
        "os_type": "computer.os.type",
        "entity_type": "base.entity_type",
        "business_name": "asset_base.ownership.business.name",
        "owner_name": "asset_base.owners.username",
    }
    _asset_field_name_mapper = {
        "asset_ip": "IP地址",
        "os_type": "操作系统类型",
        "entity_type": "资产类型",
        "business_name": "业务系统",
        "owner_name": "责任人",
    }
    _asset_relation_field_mapper = {
        "asset_ip": "network.priority_addr",
        "business_name": "asset_base.ownership.business.name",
    }

    _asset_exposure_mapper = {"exposure": "internet_mapping.exposure"}
    _asset_port_field = "network.ports"
    _asset_port_mapper = {
        "internal_ip": "ip",
        "internal_port": "number",
        "internet_ip": "internet_ip",
        "internet_port": "internet_port",
    }
    _asset_port_meta_field_mapper = {
        "ip": "network.ports.ip",
        "number": "network.ports.number",
        "internet_ip": "network.ports.internet_ip",
        "internet_port": "network.ports.internet_port",
    }
    _asset_business_field = "asset_base.businesses"
    _asset_processes_field = "computer.processes"
    _asset_adapter_properties_field = "asset_base.adapter_properties"
    _asset_db_field = "computer.dbs"
    _asset_website_field = "computer.websites"
    _asset_account_field = "computer.accounts"
    _asset_type_name_field = "base.asset_type_display_name"
    _asset_adapter_field = "base.adapters"
    _asset_type_field = "base.asset_type"
    _asset_owner_field = "asset_base.owners"

    _business_name_field = "business.name"

    __intelligence = None

    @property
    def card_type(self):
        return CardType.VUL_INSTANCE_UNIQUE

    @classmethod
    def _padding_field_info(cls, manager, meta_field_mapper, record, result):
        cls.__padding_core(manager, meta_field_mapper, cls._vul_instance_field_mapper, record, result)

    @classmethod
    def __parse_cvss(cls, cvss_vector, version=3) -> dict:
        result = {}
        if not cvss_vector:
            return result
        vectors = []
        cvss_score = None
        if version == 3:
            cvss_score = CVSS3(cvss_vector)
        elif version == 2:
            cvss_score = CVSS2(cvss_vector)
        else:
            return result
        base_score = 0
        base_severity = CVSSSecurity.NONE.value
        try:
            base_score = cvss_score.scores()[0]
            base_severity = cvss_score.severities()[0]
        except Exception as e:
            log.warning(f"Parse cvss error({e})")

        for segment in cvss_vector.split("/"):
            segment_split = segment.split(":")
            vectors.append({"name": segment_split[0], "value": segment_split[1]})
        result["base_score"] = base_score
        result["severity"] = CVSSSecurityNameMapper[CVSSSecurity(base_severity)]
        result["vector"] = cvss_vector
        result["vector_value"] = vectors
        return result

    @classmethod
    def _padding_vul_info(cls, manager, record, result):
        cve_id = cls.extract(record, cls._asset_vul_cve_id_field)
        if not cve_id:
            return
        vul_mapper = entity_service.get_entity(Category.VUL, cls._vul_cve_id_field, cve_id)
        if not vul_mapper:
            return

        _vulnerability_field_mapper = manager.default_query.find_field_to_mapper(Category.VUL)

        cls.__padding_core(manager, _vulnerability_field_mapper, cls._vul_field_mapper, vul_mapper, result)
        result = cls.build_vuln_basic_info(result)
        # cvss3.0
        cvss3 = cls.extract(vul_mapper, cls._vul_cvss_3_field)
        cvss3_vector = cvss3.get("vector") if cvss3 else None
        result["cvss3"] = cls.__parse_cvss(cvss3_vector)
        # cvss2.0
        cvss2 = cls.extract(vul_mapper, cls._vul_cvss_2_field)
        cvss2_vector = cvss2.get("vector") if cvss2 else None
        result["cvss2"] = cls.__parse_cvss(cvss2_vector, version=2)

        #   利用脚本
        exploits = cls.extract(vul_mapper, cls._vul_exploits_field)
        exp = []
        if exploits:
            sources_already_used = set()
            for exploit in exploits:
                source = exploit["source"]
                if source in sources_already_used:
                    continue
                sources_already_used.add(source)
                exp.append({"source": exploit["source"], "url": exploit["url"]})
        if exp:
            result["exp"] = exp

        # CWE
        cwes = cls.extract(record, cls._vul_cwes_field)
        impact_range = []
        if cwes:
            impact_range.append({"type": "CWE", "value": cwes})
        # CPE
        cpe = cls.extract(record, cls._vul_cpe_field)
        if cpe:
            impact_range.append({"type": "CPE", "value": cpe})
        result["impact_range"] = impact_range
        return result

    @classmethod
    def _padding_asset_info(
        cls,
        manager,
        date,
        record,
        result,
        asset_relation_info,
        asset_risk_information,
        intelligence,
    ) -> list:
        asset_intell = {}
        asset_relation = cls.extract(record, cls._asset_vul_asset_field)
        if not asset_relation:
            return None
        asset_id = asset_relation.get("rel_id")
        asset_dict = entity_service.get_entity(Category.ASSET, cls._asset_id_field, asset_id, date=date)
        if not asset_dict:
            return None
        asset_type = cls.extract(asset_dict, cls._asset_type_field)
        asset_adapters = cls.extract(asset_dict, cls._asset_adapter_field)
        if asset_adapters is None:
            asset_adapters = []
        asset_meta_field_mapper = manager.default_query.find_field_to_mapper(Category.ASSET)
        # 构建关联资产信息
        asset_relation_info["asset_ip"] = {
            "rendered_value": cls.extract(asset_dict, cls._asset_addr_ip_field),
            "value": {
                "category": Category.ASSET,
                "entity_id": asset_id,
                "entity_type": cls.extract(asset_dict, cls._asset_entity_type_field),
            },
        }
        business_name = cls.extract(asset_dict, cls._asset_relation_field_mapper["business_name"])
        business_entity = entity_service.get_entity(Category.BUSINESS, "business.full_name", business_name, date=date)
        if business_entity:
            asset_relation_info["business_name"] = {
                "rendered_value": business_name,
                "value": {
                    "category": Category.BUSINESS,
                    "entity_id": asset_id,
                    "entity_type": cls.extract(business_entity, cls._asset_entity_type_field),
                },
            }

        # 构建基础信息
        cls.__padding_core(manager, asset_meta_field_mapper, cls._asset_field_mapper, asset_dict, result)

        # 漏洞利用路径的暴露面链路
        asset_risk_information["exploit_path"] = cls.build_asset_exposure(manager, asset_meta_field_mapper, asset_dict)

        # 防护度
        protect_adapter_list = []
        adapter_instances: List[AdapterInstance] = adapter_instance_service.find_adapter_instance(
            protections=asset_type
        )
        ancestor_adapter_names = [adapter_instance.ancestor_adapter_name for adapter_instance in adapter_instances]
        if ancestor_adapter_names:
            adapters: List[Adapter] = adapter_service.find_adapter(names=ancestor_adapter_names)
            for adapter in adapters:
                if adapter.name in asset_adapters:
                    protect_adapter_list.append(
                        {"name": adapter.name, "display_name": adapter.display_name, "enabled": True}
                    )
                else:
                    protect_adapter_list.append(
                        {"name": adapter.name, "display_name": adapter.display_name, "enabled": False}
                    )
        asset_risk_information["protect_adapter_list"] = protect_adapter_list

        # 分析漏洞利用可能性
        exploit_analysia = {}
        processes = cls.extract(asset_dict, cls._asset_processes_field)
        if processes is None:
            processes = []
        vuln_package_version = cls.extract(record, "vul_instance_unique.package.version")
        vuln_package_name = cls.extract(record, "vul_instance_unique.package.name")
        is_root_privilage = False
        for process in processes:
            if process.get("gname") != vuln_package_name:
                continue
            is_root_privilage = True
        exploit_analysia["vuln_software"] = {
            "is_root_privilege": is_root_privilage,
            "label": "是否管理员权限运行",
        }

        is_privileged_system = False
        ownership_system_name = cls.extract(asset_dict, "asset_base.ownership.business.name")
        if ownership_system_name:
            businesses = cls.extract(asset_dict, cls._asset_business_field)
            if businesses is None:
                businesses = []
            for business in businesses:
                if ownership_system_name == business.get("name") and business.get("privileged_system"):
                    is_privileged_system = True
                    break
        exploit_analysia["privileged_system"] = {
            "is_privileged_system": is_privileged_system,
            "label": "是否集权系统",
        }

        # 是否包含敏感数据
        is_contain_sensitive_data = False
        business_portraiter: List[BusinessPortrait] = business_portraiter_service.find_business_portraiter(
            name=ownership_system_name
        )
        for business in business_portraiter:
            if business.contain_sensitive_data:
                is_contain_sensitive_data = True
        exploit_analysia["sensitive_data"] = {
            "is_contain_sensitive_data": is_contain_sensitive_data,
            "label": "是否包含敏感数据",
        }

        asset_risk_information["exploit_analysia"] = exploit_analysia
        result = cls.render_information(result)
        return result

    @classmethod
    def build_asset_exposure(cls, manager, asset_meta_field_mapper, asset_dict: dict) -> dict:
        ip_addr = cls.extract(asset_dict, "network.priority_addr")
        exploit_path = {"external": False}
        internal_mapping_exposures = cls.extract(asset_dict, "internet_mapping.exposure")
        if internal_mapping_exposures is not None:
            exposures_value = internal_mapping_exposures.get("value", 0)
            if exposures_value == 3:
                # 暴露外网
                exploit_path["external"] = True
                cls.__padding_core(
                    manager, asset_meta_field_mapper, cls._asset_exposure_mapper, asset_dict, exploit_path
                )
                exploit_path["exposure"] = {"asql": f"internet_service_unique.internet_ips = '{ip_addr}'"}

        return exploit_path

    def parse(self, manager, record, category, date, card_setting, setting=None, asql=None):
        meta_field_mapper = setting.get("meta_field_mapper")
        result = {"traits": self.extract(record, self._vul_instance_unique_traits_field) or []}
        asset_relation = {}
        basic_information = {}
        head_information = {}
        asset_risk_information = {}
        vuln_intelligence = {}
        intelligence = {}
        result["asset_relation"] = asset_relation
        result["head_information"] = head_information
        result["asset_risk_information"] = asset_risk_information
        result["vuln_intelligence"] = vuln_intelligence

        result["intelligence"] = {"config": self.get_intelligence(), "value": intelligence}

        self._padding_field_info(manager, meta_field_mapper, record, head_information)
        result["basic_information"] = self._padding_asset_info(
            manager,
            date,
            record,
            basic_information,
            asset_relation,
            asset_risk_information,
            intelligence,
        )
        self._padding_vul_info(manager, record, vuln_intelligence)

        self.rebuild_infor(head_information)
        self.build_intelligence(intelligence, result)
        return result

    @classmethod
    def rebuild_infor(cls, head_information):
        adapter_sources = head_information.get("adapter_sources", {})
        if adapter_sources:
            adapter_sources["rendered_value"] = [adapter["name"] for adapter in adapter_sources.get("value", [])]

    @classmethod
    def __padding_core(cls, manager, meta_field_mapper, field_mapper, record, result):
        for key_name, field_name in field_mapper.items():
            _field_value = cls.extract(record, field_name)
            _meta_field = meta_field_mapper.get(field_name)
            if _meta_field:
                result[key_name] = manager.render(_field_value, _meta_field)

    @classmethod
    def __padding_single_data(cls, manager, field_mapper, meta_field_relation, meta_field_mapper, record, result):
        for key_name, relation_name in field_mapper.items():
            _field_name = meta_field_relation[relation_name]
            _meta_field = meta_field_mapper[_field_name]
            _field_value = cls.extract(record, relation_name)
            result[key_name] = manager.render(_field_value, _meta_field)

    @classmethod
    def render_information(cls, result) -> list:
        info = []
        for field_name, display_name in cls._asset_field_name_mapper.items():
            rendered_value = result.get(field_name, {}).get("rendered_value", None)
            if rendered_value:
                info.append(
                    {
                        "name": field_name,
                        "display_name": display_name,
                        "value": rendered_value,
                    }
                )
        return info

    @classmethod
    def build_vuln_basic_info(cls, result):
        solution = result.get("solution", {}).get("rendered_value", None)
        if solution:
            result["is_solution"] = {"value": "true", "rendered_value": "是"}
        else:
            result["is_solution"] = {"value": "false", "rendered_value": "否"}
        exploit = result.get("exploits", {}).get("rendered_value", None)
        if exploit:
            result["is_exploit"] = {"value": "true", "rendered_value": "是"}
        else:
            result["is_exploit"] = {"value": "false", "rendered_value": "否"}
        return result

    @classmethod
    def get_intelligence(cls) -> dict:
        if cls.__intelligence is None:
            cls.__intelligence = setting_service.get_setting(SettingName.intelligence).value
        return cls.__intelligence

    @classmethod
    def build_intelligence(cls, intelligence, result):
        pass
